"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import ChartLineIcon from "@/components/icons/ChartLineIcon";
import ClockIcon from "@/components/icons/ClockIcon";
import TriangleAlertIcon from "@/components/icons/TriangleAlertIcon";
import HeartIcon from "@/components/icons/HeartIcon";
import { useRouter } from "next/navigation";

interface HealthMetricCardProps {
  id: string;
  title: string;
  icon: string;
  details: Record<string, any>;
  action: string;
  route?: string;
}

export function HealthMetricCard({ id, title, icon, details, action, route }: HealthMetricCardProps) {
  const router = useRouter();

  const handleClick = () => {
    if (route) {
      router.push(route);
    }
  };
  const renderIcon = () => {
    const iconProps = { width: 22, height: 22, color: "#455f84" };
    
    switch (icon) {
      case "chart-line":
        return <ChartLineIcon {...iconProps} />;
      case "clock":
        return <ClockIcon {...iconProps} />;
      case "triangle-alert":
        return <TriangleAlertIcon {...iconProps} />;
      case "heart":
        return <HeartIcon {...iconProps} />;
      default:
        return null;
    }
  };

  const renderDetails = () => {
    switch (id) {
      case "recent-symptom":
        return (
          <div className="space-y-1">
            <p className="health-body-sm">Symptom: {details.symptom}</p>
            <p className="health-body-sm">Severity: {details.severity}</p>
            <p className="health-body-sm">
              Detected: {details.detectedDate?.toLocaleDateString('en-US', { month: 'long', day: 'numeric' })}
            </p>
          </div>
        );
      case "upcoming-reminders":
        return (
          <div className="space-y-1">
            <p className="health-body-sm">Next Medication: {details.nextMedication}</p>
            <p className="health-body-sm">
              Time: {details.time?.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true })}
            </p>
          </div>
        );
      case "chronic-condition":
        return (
          <div className="space-y-1">
            <p className="health-body-sm">Condition: {details.condition}</p>
            <p className="health-body-sm">
              Next Review: {details.nextReview?.toLocaleDateString('en-US', { month: 'long', day: 'numeric' })}
            </p>
          </div>
        );
      case "key-vitals":
        return (
          <div className="space-y-1">
            <p className="health-body-sm">Heart Rate: {details.heartRate} BPM</p>
            <p className="health-body-sm">Sleep: {details.sleepHours} hrs</p>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <Card className="health-card-shadow health-rounded">
      <CardHeader className="pb-4">
        <div className="flex items-start gap-3">
          {renderIcon()}
          <div className="flex-1">
            <CardTitle className="health-body-lg mb-2">{title}</CardTitle>
            {renderDetails()}
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <Button
          variant="link"
          className="px-0 health-link-text cursor-pointer"
          onClick={handleClick}
          disabled={!route}
        >
          {action}
        </Button>
      </CardContent>
    </Card>
  );
}