"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ResponsiveContainer } from "recharts";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface ChartDataPoint {
  day: string;
  value: number;
}

interface MoodTrendChartProps {
  data: ChartDataPoint[];
}

const chartConfig = {
  value: {
    label: "Mood Level",
    color: "#14b8a6",
  },
};

export function MoodTrendChart({ data }: MoodTrendChartProps) {
  return (
    <Card className="health-card-shadow health-rounded">
      <CardHeader className="pb-2">
        <CardTitle className="health-heading-sm">Mood Trend (Last 7 days)</CardTitle>
      </CardHeader>
      <CardContent className="h-80">
        <ChartContainer config={chartConfig} className="w-full h-full">
          <Line<PERSON><PERSON> data={data} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
            <XAxis 
              dataKey="day" 
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#868686" }}
            />
            <YAxis 
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#868686" }}
              domain={[0, 100]}
            />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Line 
              type="monotone" 
              dataKey="value" 
              stroke="#14b8a6" 
              strokeWidth={3}
              dot={{ fill: "#14b8a6", strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, fill: "#14b8a6" }}
            />
          </LineChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}