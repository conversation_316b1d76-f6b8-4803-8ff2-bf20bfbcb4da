"use client";

import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { AreaChart, Area, XAxis, YAxis, CartesianGrid } from "recharts";
import ChevronDownIcon from "@/components/icons/ChevronDownIcon";

interface ChartDataPoint {
  day: string;
  value: number;
  dayIndex: number;
}

interface SymptomChartProps {
  data: ChartDataPoint[];
}

const chartConfig = {
  value: {
    label: "Symptom Intensity",
    color: "#3b82f6",
  },
};

export function SymptomChart({ data }: SymptomChartProps) {
  return (
    <div className="space-y-4">
      <h2 className="health-heading-lg">Symptom Trend Visualization</h2>
      
      <Card className="health-card-shadow health-rounded">
        <CardHeader className="flex flex-row items-center justify-between pb-4">
          <p className="health-body-lg text-[#202224]">Last 7 days</p>
          <Select defaultValue="october">
            <SelectTrigger className="w-32 border-0 bg-transparent">
              <SelectValue />
              <ChevronDownIcon width={16} height={16} color="#868686" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="october">October</SelectItem>
              <SelectItem value="november">November</SelectItem>
              <SelectItem value="december">December</SelectItem>
            </SelectContent>
          </Select>
        </CardHeader>
        
        <CardContent className="pt-0">
          <div className="flex flex-col space-y-4">
            <div className="flex justify-between text-sm text-[rgba(43,48,52,0.40)]">
              {data.map((point) => (
                <span key={point.day}>{point.day}</span>
              ))}
            </div>
            
            <div className="h-64">
              <ChartContainer config={chartConfig} className="w-full h-full">
                <AreaChart data={data} margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis 
                    dataKey="dayIndex" 
                    axisLine={false}
                    tickLine={false}
                    tick={false}
                  />
                  <YAxis 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 14, fill: "rgba(43,48,52,0.40)" }}
                    domain={[0, 8]}
                    ticks={[1, 2, 3, 4, 5, 6, 7, 8]}
                  />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Area
                    type="monotone"
                    dataKey="value"
                    stroke="#3b82f6"
                    strokeWidth={2}
                    fill="rgba(59, 130, 246, 0.1)"
                    dot={{ fill: "#3b82f6", strokeWidth: 2, r: 4 }}
                  />
                </AreaChart>
              </ChartContainer>
            </div>
            
            <div className="flex justify-between text-sm text-[rgba(43,48,52,0.40)] px-5">
              {[10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120].map((num) => (
                <span key={num}>{num}</span>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}