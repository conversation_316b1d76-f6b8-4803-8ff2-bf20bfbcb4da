"use client";

import { <PERSON><PERSON><PERSON>rumb, BreadcrumbI<PERSON>, Bread<PERSON>rumbLink, BreadcrumbList } from "@/components/ui/breadcrumb";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import ChevronRightIcon from "@/components/icons/ChevronRightIcon";
import TriangleAlertIcon from "@/components/icons/TriangleAlertIcon";

export default function ChronicConditionsPage() {
  const conditions = [
    {
      id: 1,
      name: "Migraines",
      status: "Active",
      severity: "Moderate",
      nextReview: "July 2, 2024",
      lastUpdate: "May 25, 2024",
      description: "Recurring headaches with moderate intensity, typically triggered by stress and lack of sleep."
    },
    {
      id: 2,
      name: "Chronic Fatigue",
      status: "Monitoring",
      severity: "Mild",
      nextReview: "June 15, 2024",
      lastUpdate: "May 20, 2024",
      description: "Persistent tiredness not relieved by rest, currently being monitored for patterns."
    }
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'mild':
        return 'bg-green-100 text-green-800';
      case 'moderate':
        return 'bg-yellow-100 text-yellow-800';
      case 'severe':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-red-100 text-red-800';
      case 'monitoring':
        return 'bg-blue-100 text-blue-800';
      case 'stable':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="w-full min-h-screen p-6 sm:p-10 space-y-6 bg-health-background">
      {/* Breadcrumb */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <ChevronRightIcon width={16} height={16} color="#6b7280" />
          <BreadcrumbItem>
            <span className="text-health-primary font-medium">Chronic Conditions</span>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="health-heading-xl">Chronic Condition Management</h1>
        <p className="health-body-lg text-health-secondary">
          Track and manage your chronic conditions with AI-powered insights
        </p>
      </div>

      {/* Conditions List */}
      <div className="space-y-4">
        {conditions.map((condition) => (
          <Card key={condition.id} className="health-card-shadow health-rounded">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <TriangleAlertIcon width={20} height={20} color="#455f84" />
                  <div>
                    <CardTitle className="health-body-lg">{condition.name}</CardTitle>
                    <p className="health-body-sm text-health-secondary mt-1">
                      Last updated: {condition.lastUpdate}
                    </p>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Badge className={getSeverityColor(condition.severity)}>
                    {condition.severity}
                  </Badge>
                  <Badge className={getStatusColor(condition.status)}>
                    {condition.status}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="health-body-sm text-health-secondary">
                {condition.description}
              </p>
              <div className="flex items-center justify-between">
                <div>
                  <p className="health-body-sm font-medium">Next Review</p>
                  <p className="health-body-sm text-health-accent">{condition.nextReview}</p>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    Update Status
                  </Button>
                  <Button size="sm">
                    View Plan
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="health-card-shadow health-rounded">
          <CardHeader>
            <CardTitle>Add Condition</CardTitle>
          </CardHeader>
          <CardContent>
            <Button className="w-full">
              Add New Condition
            </Button>
          </CardContent>
        </Card>

        <Card className="health-card-shadow health-rounded">
          <CardHeader>
            <CardTitle>Generate Report</CardTitle>
          </CardHeader>
          <CardContent>
            <Button variant="outline" className="w-full">
              Create Report
            </Button>
          </CardContent>
        </Card>

        <Card className="health-card-shadow health-rounded">
          <CardHeader>
            <CardTitle>AI Insights</CardTitle>
          </CardHeader>
          <CardContent>
            <Button variant="outline" className="w-full">
              Get Insights
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
