// String formatters for reminders and appointments
import { MedicationFrequency, AppointmentType } from '../mocks/enums';

export const formatTime = (time: string): string => {
  return time;
};

export const formatDate = (date: Date): string => {
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(today.getDate() + 1);
  
  if (date.toDateString() === today.toDateString()) {
    return `Today, ${date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true })}`;
  } else if (date.toDateString() === tomorrow.toDateString()) {
    return `Tomorrow, ${date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true })}`;
  } else {
    const dayName = date.toLocaleDateString('en-US', { weekday: 'long' });
    const time = date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
    return `${dayName}, ${time}`;
  }
};

export const formatFrequency = (frequency: MedicationFrequency): string => {
  return frequency;
};

export const formatAppointmentType = (type: AppointmentType): string => {
  return type;
};