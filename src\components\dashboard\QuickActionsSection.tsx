"use client";

import { Button } from "@/components/ui/button";

interface QuickAction {
  id: string;
  label: string;
  type: string;
}

interface QuickActionsSectionProps {
  actions: QuickAction[];
}

export function QuickActionsSection({ actions }: QuickActionsSectionProps) {
  const handleActionClick = (action: QuickAction) => {
    console.log("Action clicked:", action);
  };

  return (
    <section className="space-y-3 mb-6">
      <h2 className="health-heading-lg">Quick Actions</h2>
      <div className="flex flex-wrap gap-5">
        {actions.map((action) => (
          <Button
            key={action.id}
            variant="outline"
            size="sm"
            onClick={() => handleActionClick(action)}
            className="health-button-text"
          >
            {action.label}
          </Button>
        ))}
      </div>
    </section>
  );
}