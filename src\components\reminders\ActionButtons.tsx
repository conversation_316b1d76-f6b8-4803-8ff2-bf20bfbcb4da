import React from 'react';
import { Button } from '@/components/ui/button';
import CheckboxIcon from '@/components/icons/CheckboxIcon';
import ViewIcon from '@/components/icons/ViewIcon';
import EditIcon from '@/components/icons/EditIcon';
import DeleteIcon from '@/components/icons/DeleteIcon';
import EyeIcon from '@/components/icons/EyeIcon';
import RefreshIcon from '@/components/icons/RefreshIcon';

interface ActionButtonsProps {
  itemId: string;
  type: 'medication' | 'appointment';
  onAction: (id: string, action: string) => void;
}

export function ActionButtons({ itemId, type, onAction }: ActionButtonsProps) {
  if (type === 'medication') {
    return (
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="icon"
          className="h-4 w-4 p-0"
          onClick={() => onAction(itemId, 'complete')}
        >
          <CheckboxIcon width={16} height={16} color="#394c6b" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          className="h-4 w-4 p-0"
          onClick={() => onAction(itemId, 'view')}
        >
          <ViewIcon width={13} height={12} color="#394c6b" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          className="h-4 w-4 p-0"
          onClick={() => onAction(itemId, 'edit')}
        >
          <EditIcon width={12} height={12} color="#394c6b" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          className="h-4 w-4 p-0"
          onClick={() => onAction(itemId, 'delete')}
        >
          <DeleteIcon width={10} height={10} color="#394c6b" />
        </Button>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <Button
        variant="ghost"
        size="icon"
        className="h-4 w-4 p-0"
        onClick={() => onAction(itemId, 'view')}
      >
        <EyeIcon width={12} height={8} color="#394c6b" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        className="h-4 w-4 p-0"
        onClick={() => onAction(itemId, 'refresh')}
      >
        <RefreshIcon width={16} height={16} color="#394c6b" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        className="h-4 w-4 p-0"
        onClick={() => onAction(itemId, 'delete')}
      >
        <DeleteIcon width={10} height={10} color="#394c6b" />
      </Button>
    </div>
  );
}