"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

interface CircularProgressData {
  value: number;
  total: number;
}

interface CircularProgressChartProps {
  data: CircularProgressData;
}

export function CircularProgressChart({ data }: CircularProgressChartProps) {
  const percentage = (data.value / data.total) * 100;
  const circumference = 2 * Math.PI * 45; // radius = 45
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <Card className="health-card-shadow health-rounded">
      <CardHeader className="pb-2">
        <CardTitle className="health-heading-sm">Pain Level Over Time</CardTitle>
      </CardHeader>
      <CardContent className="h-80 flex items-center justify-center">
        <div className="relative w-32 h-32">
          <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 100 100">
            {/* Background circle */}
            <circle
              cx="50"
              cy="50"
              r="45"
              stroke="#e5e7eb"
              strokeWidth="8"
              fill="none"
            />
            {/* Progress circle */}
            <circle
              cx="50"
              cy="50"
              r="45"
              stroke="#455f84"
              strokeWidth="8"
              fill="none"
              strokeDasharray={strokeDasharray}
              strokeDashoffset={strokeDashoffset}
              strokeLinecap="round"
              className="transition-all duration-300 ease-in-out"
            />
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-4xl font-semibold text-health-secondary">
              {data.value}%
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}