import React from 'react';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '@/components/ui/table';
import { ActionButtons } from './ActionButtons';
import { formatDate, formatFrequency } from '@/lib/formatters';
import { MedicationFrequency } from '@/mocks/enums';

interface Medication {
  id: string;
  name: string;
  dosage: string;
  time: Date;
  frequency: MedicationFrequency;
}

interface MedicationsTableProps {
  medications: Medication[];
  onAction: (medicationId: string, action: string) => void;
}

export function MedicationsTable({ medications, onAction }: MedicationsTableProps) {
  return (
    <div className="health-table-container">
      <Table>
        <TableHeader>
          <TableRow className="health-table-header">
            <TableHead className="health-body-lg text-[#191919] font-normal px-10">Medication Name</TableHead>
            <TableHead className="health-body-lg text-[#191919] font-normal">Dosage</TableHead>
            <TableHead className="health-body-lg text-[#191919] font-normal">Time</TableHead>
            <TableHead className="health-body-lg text-[#191919] font-normal">Frequency</TableHead>
            <TableHead className="health-body-lg text-[#191919] font-normal">Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {medications.map((medication) => (
            <TableRow key={medication.id} className="health-table-row">
              <TableCell className="health-button-text px-10">{medication.name}</TableCell>
              <TableCell className="health-button-text">{medication.dosage}</TableCell>
              <TableCell className="health-button-text">
                {medication.time.toLocaleTimeString('en-US', { 
                  hour: 'numeric', 
                  minute: '2-digit', 
                  hour12: true 
                })}
              </TableCell>
              <TableCell className="health-button-text">
                {formatFrequency(medication.frequency)}
              </TableCell>
              <TableCell>
                <ActionButtons
                  itemId={medication.id}
                  type="medication"
                  onAction={onAction}
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}