"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import HappyFaceIcon from "@/components/icons/HappyFaceIcon";
import NeutralFaceIcon from "@/components/icons/NeutralFaceIcon";
import SadFaceIcon from "@/components/icons/SadFaceIcon";

type MoodType = "happy" | "neutral" | "sad" | null;

interface MoodSelectorProps {
  onMoodChange?: (mood: MoodType) => void;
}

export function MoodSelector({ onMoodChange }: MoodSelectorProps) {
  const [selectedMood, setSelectedMood] = useState<MoodType>(null);

  const handleMoodSelect = (mood: MoodType) => {
    setSelectedMood(mood);
    onMoodChange?.(mood);
  };

  return (
    <div className="space-y-2">
      <p className="health-heading-md">Mood:</p>
      <div className="flex gap-2">
        <Button
          variant="outline"
          size="icon"
          aria-label="happy"
          onClick={() => handleMoodSelect("happy")}
          className={selectedMood === "happy" ? "bg-health-background" : ""}
        >
          <HappyFaceIcon width={20} height={20} color="#41516b" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          aria-label="neutral"
          onClick={() => handleMoodSelect("neutral")}
          className={selectedMood === "neutral" ? "bg-health-background" : ""}
        >
          <NeutralFaceIcon width={20} height={20} color="#41516b" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          aria-label="sad"
          onClick={() => handleMoodSelect("sad")}
          className={selectedMood === "sad" ? "bg-health-background" : ""}
        >
          <SadFaceIcon width={24} height={24} color="#41516b" />
        </Button>
      </div>
    </div>
  );
}