"use client";

import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";

interface RecentActivity {
  id: string;
  text: string;
  action: string;
  date: Date;
}

interface ActivityTimelineProps {
  activities: RecentActivity[];
}

export function ActivityTimeline({ activities }: ActivityTimelineProps) {
  const handleActionClick = (activity: RecentActivity) => {
    console.log("Activity action clicked:", activity);
  };

  return (
    <section className="space-y-3 mb-6">
      <h2 className="health-heading-lg">Recent Activity & Insights</h2>
      <div className="space-y-2">
        {activities.map((activity) => (
          <Card key={activity.id}>
            <CardContent className="p-3 flex items-center justify-between gap-4">
              <span className="health-body-lg">{activity.text}</span>
              <Button 
                variant="link" 
                className="px-0 health-link-text"
                onClick={() => handleActionClick(activity)}
              >
                {activity.action}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  );
}