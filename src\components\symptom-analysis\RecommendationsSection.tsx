"use client";

interface RecommendationsSectionProps {
  recommendations: string[];
  healthAlerts: string[];
}

export function RecommendationsSection({ recommendations, healthAlerts }: RecommendationsSectionProps) {
  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="health-heading-sm">Localized Health Alerts</h3>
        {healthAlerts.map((alert, index) => (
          <p key={index} className="health-body-lg">{alert}</p>
        ))}
      </div>
      
      <div className="space-y-4">
        <h3 className="health-heading-sm">Recommendations</h3>
        <div className="space-y-4">
          {recommendations.map((recommendation, index) => (
            <p key={index} className="health-body-lg">{recommendation}</p>
          ))}
        </div>
      </div>
    </div>
  );
}