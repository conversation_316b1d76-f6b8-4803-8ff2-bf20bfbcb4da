"use client"

import { <PERSON> } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"

interface AppHeaderProps {
  user?: {
    name: string
    email: string
    avatar: string
  }
}

export function AppHeader({ user }: AppHeaderProps) {
  const defaultUser = {
    name: "Adebay<PERSON>",
    email: "<EMAIL>",
    avatar: "https://i.pravatar.cc/150?img=1"
  }

  const currentUser = user || defaultUser

  return (
    <header className="flex items-center justify-end px-6 py-4 bg-background border-b border-border">
      <div className="flex items-center gap-4">
        {/* Notification Bell */}
        <Button variant="ghost" size="icon" className="relative">
          <Bell size={20} className="text-health-muted" />
          {/* Optional notification dot */}
          <span className="absolute -top-1 -right-1 h-3 w-3 bg-health-error rounded-full"></span>
        </Button>

        {/* User Info */}
        <div className="flex items-center gap-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={currentUser.avatar} alt={currentUser.name} />
            <AvatarFallback className="bg-health-light text-health-secondary font-medium">
              {currentUser.name.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col">
            <span className="health-body-md font-medium text-health-secondary">
              {currentUser.name}
            </span>
            <span className="health-body-sm text-health-muted">
              {currentUser.email}
            </span>
          </div>
        </div>
      </div>
    </header>
  )
}