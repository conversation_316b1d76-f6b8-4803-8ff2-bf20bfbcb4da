# Copilot Instructions for `petals_health` User Dashboard

This document provides guidance for AI coding agents to be productive in the `petals_health` user dashboard project. It outlines the architecture, workflows, and conventions specific to this codebase.

## Project Overview

This is a [Next.js](https://nextjs.org) project structured with the `app` directory convention. It includes the following key components:

- **Authentication**: Located in `src/app/(auth)/signin` and `src/app/(auth)/signup`. These handle user sign-in and sign-up flows.
- **Dashboard**: Found in `src/app/(dashboard)`. This is the main user interface for logged-in users.
- **Components**:
  - `src/components/general`: Contains reusable navigation and layout components like `SideBar` and `team-switcher`.
  - `src/components/ui`: Includes UI primitives such as `button`, `input`, and `tooltip`.
- **Hooks**: Found in `src/hooks/context`. These manage application state and context, e.g., `useAppContext` and `useAppStore`.
- **Utilities**: Located in `src/lib/utils.ts`, providing helper functions.

## Developer Workflows

### Running the Development Server

Use the following command to start the development server:

```bash
bun dev
```

The server runs at [http://localhost:3000](http://localhost:3000).

### Building the Project

To build the project for production:

```bash
bun run build
```

### Linting and Formatting

This project uses ESLint for linting. Run the following command to check for linting issues:

```bash
bun run lint
```

### Testing

(Currently, no testing framework is explicitly mentioned in the project files. Add details here if applicable.)

## Project-Specific Conventions

- **File Naming**: Follow the Next.js convention of using `page.tsx` for route entry points and `layout.tsx` for layout components.
- **Component Organization**: General-purpose components are in `src/components/general`, while UI primitives are in `src/components/ui`.
- **State Management**: Use hooks in `src/hooks/context` for managing application state.

## Integration Points

- **External Dependencies**: The project uses `next/font` for font optimization and `Geist` as the primary font.
- **Cross-Component Communication**: Context hooks like `useAppContext` and `useAppStore` facilitate communication between components.

## Key Files and Directories

- `src/app/page.tsx`: Entry point for the main page.
- `src/app/(auth)/signin/page.tsx`: Sign-in page.
- `src/app/(auth)/signup/page.tsx`: Sign-up page.
- `src/components/general/SideBar.tsx`: Sidebar navigation component.
- `src/lib/utils.ts`: Utility functions.

## Additional Notes

- **Package Manager**: This project exclusively uses the `bun` package manager for all package management tasks. Avoid using `npm`, `yarn`, or `pnpm`.
- **Styling**: The project leverages `shadcn/ui` components for styling. Ensure consistency by adhering to the established design system.

## Role-Based Workflow

This project uses role-specific agents to ensure structured collaboration and task execution. Copilot should adhere to the following roles:

- **System Architect**: Define and maintain the overall architecture, ensuring scalability and security.
- **Supervisor**: Verify task completion and coordinate with the architect for alignment.
- **Task Planner**: Create detailed plans for improvements and feature development.
- **Task Reviewer**: Review completed tasks for quality and adherence to requirements.
- **Senior Front-End Engineer**: Implement features and address feedback from the reviewer and supervisor.

### Collaboration Guidelines

- Copilot should coordinate with the supervisor and system architect for all code edits and plans.
- Ensure tasks align with the architectural goals and project conventions.
- Follow the instructions outlined in the respective prompts under `.github/prompts/` directory.

## Notes for AI Agents

- Focus on reusability when creating new components. Check `src/components/ui` for existing primitives before adding new ones.
- Follow the established patterns for context and state management in `src/hooks/context`.
- Ensure compatibility with Next.js conventions, especially for routing and layouts.

---

If any section is unclear or incomplete, please provide feedback to improve this document.
