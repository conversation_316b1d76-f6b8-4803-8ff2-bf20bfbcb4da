// Mock data for health dashboard
export const mockRootProps = {
  user: {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    currentDate: new Date("2024-05-26T09:41:00")
  },
  healthMetrics: [
    {
      id: "recent-symptom",
      title: "Recent Symptom Analysis",
      icon: "chart-line",
      details: {
        symptom: "Headache",
        severity: "mild" as const,
        detectedDate: new Date("2024-05-25")
      },
      action: "View Full Report",
      route: "/symptom_analysis"
    },
    {
      id: "upcoming-reminders",
      title: "Upcoming Reminders",
      icon: "clock",
      details: {
        nextMedication: "Ibuprofen",
        time: new Date("2024-05-26T09:00:00")
      },
      action: "Manage AI Reminders",
      route: "/reminders"
    },
    {
      id: "chronic-condition",
      title: "Chronic Condition Snapshot",
      icon: "triangle-alert",
      details: {
        condition: "Migraines",
        nextReview: new Date("2024-07-02")
      },
      action: "View Plan",
      route: "/chronic-conditions"
    },
    {
      id: "key-vitals",
      title: "Key Vitals",
      icon: "heart",
      details: {
        heartRate: 72,
        sleepHours: 7.5
      },
      action: "Vitals History",
      route: "/vitals"
    }
  ],
  quickActions: [
    { id: "log-symptom", label: "Log New Symptom", type: "log_symptom" as const },
    { id: "track-day", label: "Track My Day", type: "track_day" as const },
    { id: "manage-meds", label: "Manage Medications", type: "manage_medications" as const },
    { id: "chat-petals", label: "Chat with Petals", type: "chat_petals" as const }
  ],
  recentActivities: [
    {
      id: "activity-1",
      text: "Symptom analysis completed - May 25",
      action: "View",
      date: new Date("2024-05-25")
    },
    {
      id: "activity-2", 
      text: "New supplement suggestion: Magnesium Glycinate",
      action: "View in Optimizer",
      date: new Date("2024-05-25")
    },
    {
      id: "activity-3",
      text: "You logged 'Anxious' yesterday", 
      action: "View Mood Logs",
      date: new Date("2024-05-25")
    },
    {
      id: "activity-4",
      text: "Personalized fatigue plan updated",
      action: "View Recovery Plan", 
      date: new Date("2024-05-24")
    }
  ],
  chartData: {
    moodTrend: [
      { day: "Mon", value: 0 },
      { day: "Tue", value: 50 },
      { day: "Wed", value: 40 },
      { day: "Thur", value: 25 },
      { day: "Fri", value: 30 },
      { day: "Sat", value: 45 },
      { day: "Sun", value: 95 }
    ],
    painLevelLine: [
      { day: "Mon", value: 5 },
      { day: "Tue", value: 60 },
      { day: "Wed", value: 45 },
      { day: "Thur", value: 30 },
      { day: "Fri", value: 35 },
      { day: "Sat", value: 50 },
      { day: "Sun", value: 85 }
    ],
    painLevelBar: [
      { day: "Mon", value: 75 },
      { day: "Tue", value: 40 },
      { day: "Wed", value: 30 },
      { day: "Thur", value: 60 },
      { day: "Fri", value: 50 },
      { day: "Sat", value: 35 },
      { day: "Sun", value: 45 }
    ],
    painLevelCircular: {
      value: 50,
      total: 100
    }
  }
};