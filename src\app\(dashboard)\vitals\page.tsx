"use client";

import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList } from "@/components/ui/breadcrumb";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import ChevronRightIcon from "@/components/icons/ChevronRightIcon";
import HeartIcon from "@/components/icons/HeartIcon";

export default function VitalsPage() {
  const currentVitals = {
    heartRate: 72,
    bloodPressure: "120/80",
    sleepHours: 7.5,
    steps: 8420,
    weight: 150,
    temperature: 98.6
  };

  const vitalHistory = [
    {
      date: "May 26, 2024",
      heartRate: 72,
      bloodPressure: "120/80",
      sleepHours: 7.5,
      steps: 8420
    },
    {
      date: "May 25, 2024",
      heartRate: 75,
      bloodPressure: "118/78",
      sleepHours: 6.8,
      steps: 7890
    },
    {
      date: "May 24, 2024",
      heartRate: 70,
      bloodPressure: "122/82",
      sleepHours: 8.2,
      steps: 9150
    }
  ];

  return (
    <div className="w-full min-h-screen p-6 sm:p-10 space-y-6 bg-health-background">
      {/* Breadcrumb */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <ChevronRightIcon width={16} height={16} color="#6b7280" />
          <BreadcrumbItem>
            <span className="text-health-primary font-medium">Vitals History</span>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="health-heading-xl">Key Vitals</h1>
        <p className="health-body-lg text-health-secondary">
          Monitor your vital signs and health metrics over time
        </p>
      </div>

      {/* Current Vitals */}
      <Card className="health-card-shadow health-rounded">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HeartIcon width={20} height={20} color="#455f84" />
            Current Vitals
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div className="text-center p-4 border border-health-border rounded-lg">
              <p className="health-body-sm text-health-secondary">Heart Rate</p>
              <p className="health-heading-sm text-health-primary">{currentVitals.heartRate}</p>
              <p className="health-body-xs text-health-secondary">BPM</p>
            </div>
            <div className="text-center p-4 border border-health-border rounded-lg">
              <p className="health-body-sm text-health-secondary">Blood Pressure</p>
              <p className="health-heading-sm text-health-primary">{currentVitals.bloodPressure}</p>
              <p className="health-body-xs text-health-secondary">mmHg</p>
            </div>
            <div className="text-center p-4 border border-health-border rounded-lg">
              <p className="health-body-sm text-health-secondary">Sleep</p>
              <p className="health-heading-sm text-health-primary">{currentVitals.sleepHours}</p>
              <p className="health-body-xs text-health-secondary">hours</p>
            </div>
            <div className="text-center p-4 border border-health-border rounded-lg">
              <p className="health-body-sm text-health-secondary">Steps</p>
              <p className="health-heading-sm text-health-primary">{currentVitals.steps.toLocaleString()}</p>
              <p className="health-body-xs text-health-secondary">today</p>
            </div>
            <div className="text-center p-4 border border-health-border rounded-lg">
              <p className="health-body-sm text-health-secondary">Weight</p>
              <p className="health-heading-sm text-health-primary">{currentVitals.weight}</p>
              <p className="health-body-xs text-health-secondary">lbs</p>
            </div>
            <div className="text-center p-4 border border-health-border rounded-lg">
              <p className="health-body-sm text-health-secondary">Temperature</p>
              <p className="health-heading-sm text-health-primary">{currentVitals.temperature}</p>
              <p className="health-body-xs text-health-secondary">°F</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Vitals History */}
      <Card className="health-card-shadow health-rounded">
        <CardHeader>
          <CardTitle>Recent History</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {vitalHistory.map((entry, index) => (
              <div key={index} className="flex items-center justify-between p-4 border border-health-border rounded-lg">
                <div>
                  <p className="health-body-lg font-medium">{entry.date}</p>
                </div>
                <div className="grid grid-cols-4 gap-6 text-center">
                  <div>
                    <p className="health-body-sm text-health-secondary">Heart Rate</p>
                    <p className="health-body-sm font-medium">{entry.heartRate} BPM</p>
                  </div>
                  <div>
                    <p className="health-body-sm text-health-secondary">Blood Pressure</p>
                    <p className="health-body-sm font-medium">{entry.bloodPressure}</p>
                  </div>
                  <div>
                    <p className="health-body-sm text-health-secondary">Sleep</p>
                    <p className="health-body-sm font-medium">{entry.sleepHours}h</p>
                  </div>
                  <div>
                    <p className="health-body-sm text-health-secondary">Steps</p>
                    <p className="health-body-sm font-medium">{entry.steps.toLocaleString()}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="health-card-shadow health-rounded">
          <CardHeader>
            <CardTitle>Log Vitals</CardTitle>
          </CardHeader>
          <CardContent>
            <Button className="w-full">
              Add New Entry
            </Button>
          </CardContent>
        </Card>

        <Card className="health-card-shadow health-rounded">
          <CardHeader>
            <CardTitle>Export Data</CardTitle>
          </CardHeader>
          <CardContent>
            <Button variant="outline" className="w-full">
              Download Report
            </Button>
          </CardContent>
        </Card>

        <Card className="health-card-shadow health-rounded">
          <CardHeader>
            <CardTitle>Set Goals</CardTitle>
          </CardHeader>
          <CardContent>
            <Button variant="outline" className="w-full">
              Manage Goals
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
