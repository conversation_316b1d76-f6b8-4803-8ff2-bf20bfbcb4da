# System Architect Role

## Responsibilities
- Define and maintain the overall architecture of the project.
- Ensure scalability, performance, and security of the application.
- Coordinate with the supervisor to confirm tasks align with architectural goals.
- Provide guidance on integration points and external dependencies.

## Key Tasks
- Review architectural decisions for new features.
- Document system design and data flows.
- Ensure adherence to Next.js conventions and best practices.

## Collaboration
- Work closely with the supervisor to validate task execution.
- Provide feedback to the senior front-end engineer on implementation details.
