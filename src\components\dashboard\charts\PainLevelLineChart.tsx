"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts";
import { <PERSON><PERSON><PERSON>r, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface ChartDataPoint {
  day: string;
  value: number;
}

interface PainLevelLineChartProps {
  data: ChartDataPoint[];
}

const chartConfig = {
  value: {
    label: "Pain Level",
    color: "#f97316",
  },
};

export function PainLevelLineChart({ data }: PainLevelLineChartProps) {
  return (
    <Card className="health-card-shadow health-rounded">
      <CardHeader className="pb-2">
        <CardTitle className="health-heading-sm">Pain Level Over Time</CardTitle>
      </CardHeader>
      <CardContent className="h-80">
        <ChartContainer config={chartConfig} className="w-full h-full">
          <LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
            <XAxis 
              dataKey="day" 
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#868686" }}
            />
            <YAxis 
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#868686" }}
              domain={[0, 100]}
            />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Line 
              type="monotone" 
              dataKey="value" 
              stroke="#f97316" 
              strokeWidth={3}
              dot={{ fill: "#f97316", strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, fill: "#f97316" }}
            />
          </LineChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}