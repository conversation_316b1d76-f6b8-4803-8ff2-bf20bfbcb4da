@import "tailwindcss";
@import "tw-animate-css";
@import url('https://fonts.googleapis.com/css2?family=Lato:wght@400;500;600;700&display=swap');

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-health-primary: var(--health-primary);
  --color-health-secondary: var(--health-secondary);
  --color-health-accent: var(--health-accent);
  --color-health-muted: var(--health-muted);
  --color-health-light: var(--health-light);
  --color-health-background: var(--health-background);
  --color-health-success: var(--health-success);
  --color-health-warning: var(--health-warning);
  --color-health-error: var(--health-error);
  --color-health-chart-teal: var(--health-chart-teal);
  --color-health-chart-orange: var(--health-chart-orange);
  --color-health-chart-blue: var(--health-chart-blue);
  --font-lato: 'Lato', sans-serif;
  --color-health-table-border: var(--health-table-border);
  --color-health-table-header-bg: var(--health-table-header-bg);
  --color-health-button-primary: var(--health-button-primary);
  --color-health-button-primary-hover: var(--health-button-primary-hover);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: #E6EFFF;
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --health-primary: #668091;
  --health-secondary: #41516b;
  --health-accent: #394c6b;
  --health-muted: #868686;
  --health-light: #d1d1d1;
  --health-background: #fffbfb;
  --health-success: #10b981;
  --health-warning: #f59e0b;
  --health-error: #ef4444;
  --health-chart-teal: #14b8a6;
  --health-chart-orange: #f97316;
  --health-chart-blue: #3b82f6;
  --health-shadow: rgba(0, 0, 0, 0.05);
  --health-table-border: #eff1f3;
  --health-table-header-bg: #f5f7fa;
  --health-button-primary: #668091;
  --health-button-primary-hover: #5a7080;
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
  --health-table-border: oklch(1 0 0 / 15%);
  --health-table-header-bg: oklch(0.269 0 0);
}

@utility health-heading-xl {
  font-family: var(--font-lato);
  font-size: 48px;
  font-weight: 400;
  color: var(--health-primary);
}

@utility health-heading-lg {
  font-family: var(--font-lato);
  font-size: 36px;
  font-weight: 400;
  color: var(--health-secondary);
}

@utility health-heading-md {
  font-family: var(--font-lato);
  font-size: 28px;
  font-weight: 400;
  color: var(--health-primary);
}

@utility health-heading-sm {
  font-family: var(--font-lato);
  font-size: 24px;
  font-weight: 400;
  color: var(--health-secondary);
}

@utility health-body-lg {
  font-family: var(--font-lato);
  font-size: 16px;
  font-weight: 400;
  color: var(--health-accent);
}

@utility health-body-md {
  font-family: var(--font-lato);
  font-size: 14px;
  font-weight: 400;
  color: var(--health-secondary);
}

@utility health-body-sm {
  font-family: var(--font-lato);
  font-size: 12px;
  font-weight: 400;
  color: var(--health-secondary);
}

@utility health-caption {
  font-family: var(--font-lato);
  font-size: 14px;
  font-weight: 400;
  color: var(--health-muted);
}

@utility health-button-text {
  font-family: var(--font-lato);
  font-size: 16px;
  font-weight: 500;
  color: var(--health-secondary);
}

@utility health-link-text {
  font-family: var(--font-lato);
  font-size: 14px;
  font-weight: 600;
  color: var(--health-secondary);
}

@utility health-card-shadow {
  box-shadow: 6px 6px 54px var(--health-shadow);
}

@utility health-rounded {
  border-radius: 14px;
}

@utility health-table-header {
  background-color: var(--health-table-header-bg);
  border-bottom: 1.14px solid var(--health-table-border);
  border-radius: 8px 8px 0px 0px;
}

@utility health-table-row {
  border-bottom: 1.14px solid var(--health-table-border);
}

@utility health-table-container {
  border-radius: 8px;
  box-shadow: 6px 6px 54px var(--health-shadow);
}

@utility health-button-primary {
  background-color: var(--health-button-primary);
  color: white;
  font-family: var(--font-lato);
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  padding: 8px 16px;
}

@utility health-button-primary:hover {
  background-color: var(--health-button-primary-hover);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}