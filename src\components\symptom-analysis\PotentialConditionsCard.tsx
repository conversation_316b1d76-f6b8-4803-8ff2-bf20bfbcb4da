"use client";

import { Card, CardContent } from "@/components/ui/card";
import { formatPercentage } from "@/mocks/symptomAnalysisMockData";

interface PotentialCondition {
  condition: string;
  probability: number;
}

interface PotentialConditionsCardProps {
  conditions: PotentialCondition[];
}

export function PotentialConditionsCard({ conditions }: PotentialConditionsCardProps) {
  return (
    <div className="space-y-4">
      <h3 className="health-heading-sm">Potential Conditions</h3>
      <div className="space-y-4">
        {conditions.map((condition, index) => (
          <Card key={index} className="health-card-shadow health-rounded p-4">
            <CardContent className="p-0 space-y-2">
              <p className="health-body-lg">{condition.condition}</p>
              <p className="health-heading-sm">{formatPercentage(condition.probability)}</p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}