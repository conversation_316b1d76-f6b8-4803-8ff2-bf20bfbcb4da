// User types
export interface User {
  id: string
  email: string
  firstName?: string
  lastName?: string
  avatar?: string
  role: 'patient' | 'provider' | 'admin'
  isEmailVerified: boolean
  createdAt: string
  updatedAt: string
}

// Authentication state
export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  token: string | null
}

// App UI state
export interface UIState {
  sidebarOpen: boolean
  theme: 'light' | 'dark' | 'system'
  notifications: Notification[]
  loadingStates: Record<string, boolean>
  modals: Record<string, boolean>
}

// Notification type
export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  duration?: number
  createdAt: Date
}

// App settings state
export interface SettingsState {
  language: string
  timezone: string
  emailNotifications: boolean
  pushNotifications: boolean
  autoSave: boolean
}

// Navigation state
export interface NavigationState {
  currentRoute: string
  previousRoute: string
  breadcrumbs: Breadcrumb[]
}

export interface Breadcrumb {
  label: string
  href: string
  active: boolean
}

// Form state
export interface FormState {
  dirty: boolean
  isValid: boolean
  errors: Record<string, string>
  touched: Record<string, boolean>
}

// Main app state interface
export interface AppState {
  // Core states
  auth: AuthState
  ui: UIState
  settings: SettingsState
  navigation: NavigationState
  
  // Computed states
  isOnline: boolean
  isMobile: boolean
  
  // Form states
  forms: Record<string, FormState>
}

// Initial state values
export const initialAuthState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  token: null,
}

export const initialUIState: UIState = {
  sidebarOpen: false,
  theme: 'system',
  notifications: [],
  loadingStates: {},
  modals: {},
}

export const initialSettingsState: SettingsState = {
  language: 'en',
  timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  emailNotifications: true,
  pushNotifications: true,
  autoSave: true,
}

export const initialNavigationState: NavigationState = {
  currentRoute: '/',
  previousRoute: '/',
  breadcrumbs: [],
}

export const initialAppState: AppState = {
  auth: initialAuthState,
  ui: initialUIState,
  settings: initialSettingsState,
  navigation: initialNavigationState,
  isOnline: true, // Default to true, will be updated on client mount
  isMobile: false,
  forms: {},
}