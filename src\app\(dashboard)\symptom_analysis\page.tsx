"use client";

import { B<PERSON><PERSON>rumb, BreadcrumbItem, Bread<PERSON>rumbLink, BreadcrumbList } from "@/components/ui/breadcrumb";
import { SymptomOverviewCard } from "@/components/symptom-analysis/SymptomOverviewCard";
import { AIInsightSection } from "@/components/symptom-analysis/AIInsightSection";
import { PotentialConditionsCard } from "@/components/symptom-analysis/PotentialConditionsCard";
import { RecommendationsSection } from "@/components/symptom-analysis/RecommendationsSection";
import { QuickActionsSection } from "@/components/symptom-analysis/QuickActionsSection";
import { SymptomChart } from "@/components/symptom-analysis/SymptomChart";
import { HistoricalDataTable } from "@/components/symptom-analysis/HistoricalDataTable";
import ChevronRightIcon from "@/components/icons/ChevronRightIcon";
import { mockRootProps, formatDate, formatTime } from "@/mocks/symptomAnalysisMockData";

export default function SymptomAnalysisPage() {
  const {
    symptomData,
    aiInsights,
    potentialConditions,
    healthAlerts,
    recommendations,
    chartData,
    historicalData
  } = mockRootProps;

  return (
    <div className="w-full min-h-screen p-6 sm:p-10 space-y-6 bg-health-background">
      {/* Breadcrumb Navigation */}
      <Breadcrumb>
        <BreadcrumbList className="flex items-center gap-1">
          <BreadcrumbItem>
            <BreadcrumbLink href="/" className="health-body-lg text-[#868686]">
              User Dashboard
            </BreadcrumbLink>
          </BreadcrumbItem>
          <ChevronRightIcon width={7} height={13} color="#868686" />
          <BreadcrumbItem>
            <span className="health-body-lg text-[#868686]">Recent Symptom Analysis</span>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Main Heading */}
      <h1 className="health-heading-lg">{symptomData.type}</h1>

      {/* Symptom Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
        <SymptomOverviewCard
          title={symptomData.type}
          value={`Severity: ${symptomData.severity}`}
        />
        <SymptomOverviewCard
          title="Detected Date"
          value={formatDate(symptomData.detectedDate)}
        />
        <SymptomOverviewCard
          title="Detected Time"
          value={formatTime(symptomData.detectedTime)}
        />
        <SymptomOverviewCard
          title="Associated Factors"
          value={symptomData.associatedFactors.join(", ")}
        />
      </div>

      {/* AI Insight Section */}
      <AIInsightSection
        heartRate={aiInsights.heartRate}
        airQuality={aiInsights.airQuality}
        sleepHours={aiInsights.sleepHours}
        analysisText={aiInsights.analysisText}
      />

      {/* Potential Conditions */}
      <PotentialConditionsCard conditions={potentialConditions} />

      {/* Recommendations and Health Alerts */}
      <RecommendationsSection
        recommendations={recommendations}
        healthAlerts={healthAlerts}
      />

      {/* Quick Actions */}
      <QuickActionsSection />

      {/* Symptom Trend Chart */}
      <SymptomChart data={chartData} />

      {/* Historical Data Table */}
      <HistoricalDataTable
        data={historicalData}
        analysisText={aiInsights.analysisText}
      />
    </div>
  );
}