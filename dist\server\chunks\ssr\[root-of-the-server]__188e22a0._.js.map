{"version": 3, "sources": [], "sections": [{"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_e531dabc-module__QGiZLq__className\",\n  \"variable\": \"geist_e531dabc-module__QGiZLq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_68a01160-module__YLcDdW__className\",\n  \"variable\": \"geist_mono_68a01160-module__YLcDdW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nconst MO<PERSON>LE_BREAKPOINT = 768\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    }\r\n    mql.addEventListener(\"change\", onChange)\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    return () => mql.removeEventListener(\"change\", onChange)\r\n  }, [])\r\n\r\n  return !!isMobile\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,qMAAA,CAAA,WAAc,CAAsB;IAEpE,qMAAA,CAAA,YAAe,CAAC;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Separator({\r\n  className,\r\n  orientation = \"horizontal\",\r\n  decorative = true,\r\n  ...props\r\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\r\n  return (\r\n    <SeparatorPrimitive.Root\r\n      data-slot=\"separator\"\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\r\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\r\n}\r\n\r\nfunction SheetTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\r\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\r\n}\r\n\r\nfunction SheetClose({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\r\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\r\n}\r\n\r\nfunction SheetPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\r\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\r\n}\r\n\r\nfunction SheetOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\r\n  return (\r\n    <SheetPrimitive.Overlay\r\n      data-slot=\"sheet-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetContent({\r\n  className,\r\n  children,\r\n  side = \"right\",\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\r\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\r\n}) {\r\n  return (\r\n    <SheetPortal>\r\n      <SheetOverlay />\r\n      <SheetPrimitive.Content\r\n        data-slot=\"sheet-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n          side === \"right\" &&\r\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\r\n          side === \"left\" &&\r\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\r\n          side === \"top\" &&\r\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\r\n          side === \"bottom\" &&\r\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\r\n          <XIcon className=\"size-4\" />\r\n          <span className=\"sr-only\">Close</span>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>\r\n  )\r\n}\r\n\r\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-header\"\r\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-footer\"\r\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\r\n  return (\r\n    <SheetPrimitive.Title\r\n      data-slot=\"sheet-title\"\r\n      className={cn(\"text-foreground font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\r\n  return (\r\n    <SheetPrimitive.Description\r\n      data-slot=\"sheet-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sheet,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot=\"tooltip-provider\"\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction Tooltip({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\r\n    </TooltipProvider>\r\n  )\r\n}\r\n\r\nfunction TooltipTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot=\"tooltip-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  )\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,8OAAC,mKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,8OAAC;kBACC,cAAA,8OAAC,mKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,8OAAC,mKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, VariantProps } from \"class-variance-authority\"\r\nimport { PanelLeftIcon } from \"lucide-react\"\r\n\r\nimport { useIsMobile } from \"@/hooks/use-mobile\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Separator } from \"@/components/ui/separator\"\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetHeader,\r\n  SheetTitle,\r\n} from \"@/components/ui/sheet\"\r\nimport { Skeleton } from \"@/components/ui/skeleton\"\r\nimport {\r\n  Toolt<PERSON>,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\"\r\n\r\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\"\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\r\nconst SIDEBAR_WIDTH = \"16rem\"\r\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\r\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\r\n\r\ntype SidebarContextProps = {\r\n  state: \"expanded\" | \"collapsed\"\r\n  open: boolean\r\n  setOpen: (open: boolean) => void\r\n  openMobile: boolean\r\n  setOpenMobile: (open: boolean) => void\r\n  isMobile: boolean\r\n  toggleSidebar: () => void\r\n}\r\n\r\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null)\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext)\r\n  if (!context) {\r\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nfunction SidebarProvider({\r\n  defaultOpen = true,\r\n  open: openProp,\r\n  onOpenChange: setOpenProp,\r\n  className,\r\n  style,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  defaultOpen?: boolean\r\n  open?: boolean\r\n  onOpenChange?: (open: boolean) => void\r\n}) {\r\n  const isMobile = useIsMobile()\r\n  const [openMobile, setOpenMobile] = React.useState(false)\r\n\r\n  // This is the internal state of the sidebar.\r\n  // We use openProp and setOpenProp for control from outside the component.\r\n  const [_open, _setOpen] = React.useState(defaultOpen)\r\n  const open = openProp ?? _open\r\n  const setOpen = React.useCallback(\r\n    (value: boolean | ((value: boolean) => boolean)) => {\r\n      const openState = typeof value === \"function\" ? value(open) : value\r\n      if (setOpenProp) {\r\n        setOpenProp(openState)\r\n      } else {\r\n        _setOpen(openState)\r\n      }\r\n\r\n      // This sets the cookie to keep the sidebar state.\r\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\r\n    },\r\n    [setOpenProp, open]\r\n  )\r\n\r\n  // Helper to toggle the sidebar.\r\n  const toggleSidebar = React.useCallback(() => {\r\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open)\r\n  }, [isMobile, setOpen, setOpenMobile])\r\n\r\n  // Adds a keyboard shortcut to toggle the sidebar.\r\n  React.useEffect(() => {\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (\r\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\r\n        (event.metaKey || event.ctrlKey)\r\n      ) {\r\n        event.preventDefault()\r\n        toggleSidebar()\r\n      }\r\n    }\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown)\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown)\r\n  }, [toggleSidebar])\r\n\r\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n  // This makes it easier to style the sidebar with Tailwind classes.\r\n  const state = open ? \"expanded\" : \"collapsed\"\r\n\r\n  const contextValue = React.useMemo<SidebarContextProps>(\r\n    () => ({\r\n      state,\r\n      open,\r\n      setOpen,\r\n      isMobile,\r\n      openMobile,\r\n      setOpenMobile,\r\n      toggleSidebar,\r\n    }),\r\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n  )\r\n\r\n  return (\r\n    <SidebarContext.Provider value={contextValue}>\r\n      <TooltipProvider delayDuration={0}>\r\n        <div\r\n          data-slot=\"sidebar-wrapper\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH,\r\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\r\n              ...style,\r\n            } as React.CSSProperties\r\n          }\r\n          className={cn(\r\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      </TooltipProvider>\r\n    </SidebarContext.Provider>\r\n  )\r\n}\r\n\r\nfunction Sidebar({\r\n  side = \"left\",\r\n  variant = \"sidebar\",\r\n  collapsible = \"offcanvas\",\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  side?: \"left\" | \"right\"\r\n  variant?: \"sidebar\" | \"floating\" | \"inset\"\r\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\"\r\n}) {\r\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\r\n\r\n  if (collapsible === \"none\") {\r\n    return (\r\n      <div\r\n        data-slot=\"sidebar\"\r\n        className={cn(\r\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n      </div>\r\n    )\r\n  }\r\n\r\n  if (isMobile) {\r\n    return (\r\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n        <SheetContent\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar\"\r\n          data-mobile=\"true\"\r\n          className=\"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\r\n            } as React.CSSProperties\r\n          }\r\n          side={side}\r\n        >\r\n          <SheetHeader className=\"sr-only\">\r\n            <SheetTitle>Sidebar</SheetTitle>\r\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\r\n          </SheetHeader>\r\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\r\n        </SheetContent>\r\n      </Sheet>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"group peer text-sidebar-foreground hidden md:block\"\r\n      data-state={state}\r\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\r\n      data-variant={variant}\r\n      data-side={side}\r\n      data-slot=\"sidebar\"\r\n    >\r\n      {/* This is what handles the sidebar gap on desktop */}\r\n      <div\r\n        data-slot=\"sidebar-gap\"\r\n        className={cn(\r\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\r\n          \"group-data-[collapsible=offcanvas]:w-0\",\r\n          \"group-data-[side=right]:rotate-180\",\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\"\r\n        )}\r\n      />\r\n      <div\r\n        data-slot=\"sidebar-container\"\r\n        className={cn(\r\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\r\n          side === \"left\"\r\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\r\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\r\n          // Adjust the padding for floating and inset variants.\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <div\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar-inner\"\r\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\r\n        >\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction SidebarTrigger({\r\n  className,\r\n  onClick,\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { toggleSidebar } = useSidebar()\r\n\r\n  return (\r\n    <Button\r\n      data-sidebar=\"trigger\"\r\n      data-slot=\"sidebar-trigger\"\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn(\"size-7\", className)}\r\n      onClick={(event) => {\r\n        onClick?.(event)\r\n        toggleSidebar()\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeftIcon />\r\n      <span className=\"sr-only\">Toggle Sidebar</span>\r\n    </Button>\r\n  )\r\n}\r\n\r\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\r\n  const { toggleSidebar } = useSidebar()\r\n\r\n  return (\r\n    <button\r\n      data-sidebar=\"rail\"\r\n      data-slot=\"sidebar-rail\"\r\n      aria-label=\"Toggle Sidebar\"\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title=\"Toggle Sidebar\"\r\n      className={cn(\r\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\r\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\r\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\r\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\r\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\r\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\r\n  return (\r\n    <main\r\n      data-slot=\"sidebar-inset\"\r\n      className={cn(\r\n        \"bg-background relative flex w-full flex-1 flex-col\",\r\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Input>) {\r\n  return (\r\n    <Input\r\n      data-slot=\"sidebar-input\"\r\n      data-sidebar=\"input\"\r\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-header\"\r\n      data-sidebar=\"header\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-footer\"\r\n      data-sidebar=\"footer\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Separator>) {\r\n  return (\r\n    <Separator\r\n      data-slot=\"sidebar-separator\"\r\n      data-sidebar=\"separator\"\r\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-content\"\r\n      data-sidebar=\"content\"\r\n      className={cn(\r\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group\"\r\n      data-sidebar=\"group\"\r\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroupLabel({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"div\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-label\"\r\n      data-sidebar=\"group-label\"\r\n      className={cn(\r\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroupAction({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-action\"\r\n      data-sidebar=\"group-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroupContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group-content\"\r\n      data-sidebar=\"group-content\"\r\n      className={cn(\"w-full text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu\"\r\n      data-sidebar=\"menu\"\r\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-item\"\r\n      data-sidebar=\"menu-item\"\r\n      className={cn(\"group/menu-item relative\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\r\n        outline:\r\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\r\n      },\r\n      size: {\r\n        default: \"h-8 text-sm\",\r\n        sm: \"h-7 text-xs\",\r\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction SidebarMenuButton({\r\n  asChild = false,\r\n  isActive = false,\r\n  variant = \"default\",\r\n  size = \"default\",\r\n  tooltip,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean\r\n  isActive?: boolean\r\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>\r\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n  const { isMobile, state } = useSidebar()\r\n\r\n  const button = (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-button\"\r\n      data-sidebar=\"menu-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n\r\n  if (!tooltip) {\r\n    return button\r\n  }\r\n\r\n  if (typeof tooltip === \"string\") {\r\n    tooltip = {\r\n      children: tooltip,\r\n    }\r\n  }\r\n\r\n  return (\r\n    <Tooltip>\r\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n      <TooltipContent\r\n        side=\"right\"\r\n        align=\"center\"\r\n        hidden={state !== \"collapsed\" || isMobile}\r\n        {...tooltip}\r\n      />\r\n    </Tooltip>\r\n  )\r\n}\r\n\r\nfunction SidebarMenuAction({\r\n  className,\r\n  asChild = false,\r\n  showOnHover = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean\r\n  showOnHover?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-action\"\r\n      data-sidebar=\"menu-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        showOnHover &&\r\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuBadge({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-badge\"\r\n      data-sidebar=\"menu-badge\"\r\n      className={cn(\r\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\r\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSkeleton({\r\n  className,\r\n  showIcon = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  showIcon?: boolean\r\n}) {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`\r\n  }, [])\r\n\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-skeleton\"\r\n      data-sidebar=\"menu-skeleton\"\r\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\r\n      {...props}\r\n    >\r\n      {showIcon && (\r\n        <Skeleton\r\n          className=\"size-4 rounded-md\"\r\n          data-sidebar=\"menu-skeleton-icon\"\r\n        />\r\n      )}\r\n      <Skeleton\r\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\r\n        data-sidebar=\"menu-skeleton-text\"\r\n        style={\r\n          {\r\n            \"--skeleton-width\": width,\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu-sub\"\r\n      data-sidebar=\"menu-sub\"\r\n      className={cn(\r\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSubItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-sub-item\"\r\n      data-sidebar=\"menu-sub-item\"\r\n      className={cn(\"group/menu-sub-item relative\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSubButton({\r\n  asChild = false,\r\n  size = \"md\",\r\n  isActive = false,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean\r\n  size?: \"sm\" | \"md\"\r\n  isActive?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-sub-button\"\r\n      data-sidebar=\"menu-sub-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\r\n        size === \"sm\" && \"text-xs\",\r\n        size === \"md\" && \"text-sm\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AApBA;;;;;;;;;;;;;;AA2BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,qMAAA,CAAA,gBAAmB,CAA6B;AAEvE,SAAS;IACP,MAAM,UAAU,qMAAA,CAAA,aAAgB,CAAC;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,gBAAgB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,qMAAA,CAAA,WAAc,CAAC;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,qMAAA,CAAA,WAAc,CAAC;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,qMAAA,CAAA,cAAiB,CAC/B,CAAC;QACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;QAC9D,IAAI,aAAa;YACf,YAAY;QACd,OAAO;YACL,SAAS;QACX;QAEA,kDAAkD;QAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;IACpG,GACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,qMAAA,CAAA,cAAiB,CAAC;QACtC,OAAO,WAAW,cAAc,CAAC,OAAS,CAAC,QAAQ,QAAQ,CAAC,OAAS,CAAC;IACxE,GAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,qMAAA,CAAA,YAAe,CAAC;QACd,MAAM,gBAAgB,CAAC;YACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;gBACA,MAAM,cAAc;gBACpB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,qMAAA,CAAA,UAAa,CAChC,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,8OAAC,mIAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,8OAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,QAAQ,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,8OAAC,iIAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,8OAAC,iIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC,iIAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,8OAAC,iIAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,8OAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,8OAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,8OAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,8OAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC;IACpC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,8OAAC,oNAAA,CAAA,gBAAa;;;;;0BACd,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAuC;IAC1E,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAqC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACgC;IACnC,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACoC;IACvC,qBACE,8OAAC,qIAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD;IACpD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD;IACvD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAmC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAC1E,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,MAAM,4BAA4B,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAClC,qzBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C;IAChD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC3D,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC,mIAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,8OAAC,mIAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oVACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ;IACC,kCAAkC;IAClC,MAAM,QAAQ,qMAAA,CAAA,UAAa,CAAC;QAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;IAClD,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/general/SideBar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport {\r\n  LayoutGrid,\r\n  MessageSquareText,\r\n  ActivitySquare,\r\n  BrainCircuit,\r\n  ClipboardCheck,\r\n  BookOpen,\r\n  History,\r\n  Settings,\r\n} from \"lucide-react\"\r\n\r\nimport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  Sidebar<PERSON>ooter,\r\n  SidebarHeader,\r\n  SidebarRail,\r\n  SidebarMenu,\r\n  SidebarMenuItem,\r\n  SidebarMenuButton,\r\n  SidebarTrigger,\r\n  useSidebar\r\n} from \"@/components/ui/sidebar\"\r\n\r\nimport Image from \"next/image\"\r\n\r\n// Petals AI sidebar data matching the mock\r\nconst data = {\r\n  user: {\r\n    name: \"Petals User\",\r\n    email: \"<EMAIL>\",\r\n    avatar: \"/images/avatar-placeholder.png\",\r\n  },\r\n  teams: {\r\n    name: \"Petals AI\",\r\n    logo: \"/images/petals_logo.svg\",\r\n    plan: \"Pro\",\r\n  },\r\n  nav: [\r\n    {\r\n      title: \"Dashboard\",\r\n      url: \"/\",\r\n      icon: LayoutGrid,\r\n    },\r\n    {\r\n      title: \"Chat with Petals\",\r\n      url: \"/chat\",\r\n      icon: MessageSquareText,\r\n    },\r\n    {\r\n      title: \"Symptom Contextualizer\",\r\n      url: \"/symptoms\",\r\n      icon: ActivitySquare,\r\n    },\r\n    {\r\n      title: \"Pain & Fatigue Companion\",\r\n      url: \"/companion\",\r\n      icon: BrainCircuit,\r\n    },\r\n    {\r\n      title: \"Medication Optimizer\",\r\n      url: \"/medications\",\r\n      icon: ClipboardCheck,\r\n    },\r\n    {\r\n      title: \"Menstrual Journal\",\r\n      url: \"/menstrual\",\r\n      icon: BookOpen,\r\n    },\r\n    {\r\n      title: \"History\",\r\n      url: \"/history\",\r\n      icon: History,\r\n    },\r\n  ],\r\n}\r\n\r\n// Petals Logo Component\r\nfunction PetalsLogo({ className }: { className?: string }) {\r\n  return (\r\n    <Image\r\n      src=\"/images/petals_logo.svg\"\r\n      alt=\"Petals AI Logo\"\r\n      width={32}\r\n      height={32}\r\n      className={className}\r\n    />\r\n  )\r\n}\r\n\r\nexport function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {\r\n  // Get current path to determine active menu item\r\n  const [currentPath, setCurrentPath] = React.useState(\"/\")\r\n  const [isHovered, setIsHovered] = React.useState(false)\r\n  const { state } = useSidebar()\r\n\r\n  React.useEffect(() => {\r\n    setCurrentPath(window.location.pathname)\r\n  }, [])\r\n\r\n  const isCollapsed = state === \"collapsed\"\r\n\r\n  return (\r\n    <Sidebar\r\n      collapsible=\"icon\"\r\n      {...props}\r\n      className=\"bg-sidebar data-[state=collapsed]:w-24\"\r\n      onMouseEnter={() => setIsHovered(true)}\r\n      onMouseLeave={() => setIsHovered(false)}\r\n    >\r\n      <SidebarHeader className={`py-6 ${isCollapsed ? 'px-2' : 'px-4'}`}>\r\n        <SidebarMenu>\r\n          <SidebarMenuItem>\r\n            <div className={`flex items-center w-full ${isCollapsed ? 'justify-center' : 'justify-between'}`}>\r\n              {/* When collapsed: show logo by default, show trigger on hover */}\r\n              {/* When expanded: show logo + text and trigger */}\r\n              {isCollapsed ? (\r\n                isHovered ? (\r\n                  <SidebarTrigger className=\"w-8 h-8\" />\r\n                ) : (\r\n                  <SidebarMenuButton\r\n                    size=\"sm\"\r\n                    className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground w-8 h-8 p-0\"\r\n                  >\r\n                    <div className=\"text-sidebar-primary-foreground flex items-center justify-center w-full h-full\">\r\n                      <PetalsLogo className=\"size-5\" />\r\n                    </div>\r\n                  </SidebarMenuButton>\r\n                )\r\n              ) : (\r\n                <>\r\n                  <SidebarMenuButton\r\n                    size=\"lg\"\r\n                    className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\"\r\n                  >\r\n                    <div className=\"text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg\">\r\n                      <PetalsLogo className=\"size-6\" />\r\n                    </div>\r\n                    <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                      <span className=\"truncate font-medium\">{data.teams.name}</span>\r\n                      <span className=\"truncate text-xs\">{data.teams.plan}</span>\r\n                    </div>\r\n                  </SidebarMenuButton>\r\n                  <SidebarTrigger className=\"ml-2\" />\r\n                </>\r\n              )}\r\n            </div>\r\n          </SidebarMenuItem>\r\n        </SidebarMenu>\r\n      </SidebarHeader>\r\n\r\n      <SidebarContent className={`py-2 ${isCollapsed ? 'px-2' : 'px-4'}`}>\r\n        <SidebarMenu className=\"space-y-1\">\r\n          {data.nav.map((item, index) => {\r\n            const isActive = index === 0; // Example logic\r\n            const baseClassName = isActive\r\n              ? 'rounded-lg transition-colors bg-green-600 text-white hover:bg-green-700'\r\n              : 'rounded-lg transition-colors text-gray-700 hover:bg-white/50';\r\n\r\n            const buttonClassName = isCollapsed\r\n              ? `${baseClassName} h-10 w-10 p-0`\r\n              : `${baseClassName} h-12 px-3`;\r\n\r\n            return (\r\n              <SidebarMenuItem key={item.title}>\r\n                <SidebarMenuButton\r\n                  asChild\r\n                  tooltip={item.title}\r\n                  className={buttonClassName}\r\n                >\r\n                  <a href={item.url} className={`flex items-center ${isCollapsed ? 'justify-center' : 'gap-3'}`}>\r\n                    <item.icon className=\"size-5\" />\r\n                    {!isCollapsed && <span className=\"font-medium\">{item.title}</span>}\r\n                  </a>\r\n                </SidebarMenuButton>\r\n              </SidebarMenuItem>\r\n            );\r\n          })}\r\n        </SidebarMenu>\r\n      </SidebarContent>\r\n\r\n      <SidebarFooter className={`py-4 ${isCollapsed ? 'px-2' : 'px-4'}`}>\r\n        <SidebarMenu>\r\n          <SidebarMenuItem>\r\n            <SidebarMenuButton\r\n              asChild\r\n              tooltip=\"Profile & Settings\"\r\n              className={`\r\n                rounded-lg transition-colors\r\n                ${currentPath === '/settings'\r\n                  ? 'bg-green-600 text-white hover:bg-green-700'\r\n                  : 'text-gray-700 hover:bg-white/50'\r\n                }\r\n                ${isCollapsed ? 'h-10 w-10 p-0' : 'h-12 px-3'}\r\n              `}\r\n            >\r\n              <a href=\"/settings\" className={`flex items-center ${isCollapsed ? 'justify-center' : 'gap-3'}`}>\r\n                <Settings className=\"size-5\" />\r\n                {!isCollapsed && <span className=\"font-medium\">Profile & Settings</span>}\r\n              </a>\r\n            </SidebarMenuButton>\r\n          </SidebarMenuItem>\r\n        </SidebarMenu>\r\n      </SidebarFooter>\r\n\r\n      <SidebarRail />\r\n    </Sidebar>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAaA;AA3BA;;;;;;AA6BA,2CAA2C;AAC3C,MAAM,OAAO;IACX,MAAM;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;IACV;IACA,OAAO;QACL,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA,KAAK;QACH;YACE,OAAO;YACP,KAAK;YACL,MAAM,kNAAA,CAAA,aAAU;QAClB;QACA;YACE,OAAO;YACP,KAAK;YACL,MAAM,oOAAA,CAAA,oBAAiB;QACzB;QACA;YACE,OAAO;YACP,KAAK;YACL,MAAM,0NAAA,CAAA,iBAAc;QACtB;QACA;YACE,OAAO;YACP,KAAK;YACL,MAAM,sNAAA,CAAA,eAAY;QACpB;QACA;YACE,OAAO;YACP,KAAK;YACL,MAAM,0NAAA,CAAA,iBAAc;QACtB;QACA;YACE,OAAO;YACP,KAAK;YACL,MAAM,8MAAA,CAAA,WAAQ;QAChB;QACA;YACE,OAAO;YACP,KAAK;YACL,MAAM,wMAAA,CAAA,UAAO;QACf;KACD;AACH;AAEA,wBAAwB;AACxB,SAAS,WAAW,EAAE,SAAS,EAA0B;IACvD,qBACE,8OAAC,6HAAA,CAAA,UAAK;QACJ,KAAI;QACJ,KAAI;QACJ,OAAO;QACP,QAAQ;QACR,WAAW;;;;;;AAGjB;AAEO,SAAS,WAAW,EAAE,GAAG,OAA6C;IAC3E,iDAAiD;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,qMAAA,CAAA,WAAc,CAAC;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,qMAAA,CAAA,WAAc,CAAC;IACjD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IAE3B,qMAAA,CAAA,YAAe,CAAC;QACd,eAAe,OAAO,QAAQ,CAAC,QAAQ;IACzC,GAAG,EAAE;IAEL,MAAM,cAAc,UAAU;IAE9B,qBACE,8OAAC,mIAAA,CAAA,UAAO;QACN,aAAY;QACX,GAAG,KAAK;QACT,WAAU;QACV,cAAc,IAAM,aAAa;QACjC,cAAc,IAAM,aAAa;;0BAEjC,8OAAC,mIAAA,CAAA,gBAAa;gBAAC,WAAW,CAAC,KAAK,EAAE,cAAc,SAAS,QAAQ;0BAC/D,cAAA,8OAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,mIAAA,CAAA,kBAAe;kCACd,cAAA,8OAAC;4BAAI,WAAW,CAAC,yBAAyB,EAAE,cAAc,mBAAmB,mBAAmB;sCAG7F,cACC,0BACE,8OAAC,mIAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;qDAE1B,8OAAC,mIAAA,CAAA,oBAAiB;gCAChB,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAW,WAAU;;;;;;;;;;;;;;;qDAK5B;;kDACE,8OAAC,mIAAA,CAAA,oBAAiB;wCAChB,MAAK;wCACL,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAW,WAAU;;;;;;;;;;;0DAExB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAwB,KAAK,KAAK,CAAC,IAAI;;;;;;kEACvD,8OAAC;wDAAK,WAAU;kEAAoB,KAAK,KAAK,CAAC,IAAI;;;;;;;;;;;;;;;;;;kDAGvD,8OAAC,mIAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQtC,8OAAC,mIAAA,CAAA,iBAAc;gBAAC,WAAW,CAAC,KAAK,EAAE,cAAc,SAAS,QAAQ;0BAChE,cAAA,8OAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACpB,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM;wBACnB,MAAM,WAAW,UAAU,GAAG,gBAAgB;wBAC9C,MAAM,gBAAgB,WAClB,4EACA;wBAEJ,MAAM,kBAAkB,cACpB,GAAG,cAAc,cAAc,CAAC,GAChC,GAAG,cAAc,UAAU,CAAC;wBAEhC,qBACE,8OAAC,mIAAA,CAAA,kBAAe;sCACd,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;gCAChB,OAAO;gCACP,SAAS,KAAK,KAAK;gCACnB,WAAW;0CAEX,cAAA,8OAAC;oCAAE,MAAM,KAAK,GAAG;oCAAE,WAAW,CAAC,kBAAkB,EAAE,cAAc,mBAAmB,SAAS;;sDAC3F,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,CAAC,6BAAe,8OAAC;4CAAK,WAAU;sDAAe,KAAK,KAAK;;;;;;;;;;;;;;;;;2BAR1C,KAAK,KAAK;;;;;oBAapC;;;;;;;;;;;0BAIJ,8OAAC,mIAAA,CAAA,gBAAa;gBAAC,WAAW,CAAC,KAAK,EAAE,cAAc,SAAS,QAAQ;0BAC/D,cAAA,8OAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,mIAAA,CAAA,kBAAe;kCACd,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;4BAChB,OAAO;4BACP,SAAQ;4BACR,WAAW,CAAC;;gBAEV,EAAE,gBAAgB,cACd,+CACA,kCACH;gBACD,EAAE,cAAc,kBAAkB,YAAY;cAChD,CAAC;sCAED,cAAA,8OAAC;gCAAE,MAAK;gCAAY,WAAW,CAAC,kBAAkB,EAAE,cAAc,mBAAmB,SAAS;;kDAC5F,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCACnB,CAAC,6BAAe,8OAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzD,8OAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;AAGlB", "debugId": null}}, {"offset": {"line": 1480, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/hooks/context/useAppState.tsx"], "sourcesContent": ["// User types\r\nexport interface User {\r\n  id: string\r\n  email: string\r\n  firstName?: string\r\n  lastName?: string\r\n  avatar?: string\r\n  role: 'patient' | 'provider' | 'admin'\r\n  isEmailVerified: boolean\r\n  createdAt: string\r\n  updatedAt: string\r\n}\r\n\r\n// Authentication state\r\nexport interface AuthState {\r\n  user: User | null\r\n  isAuthenticated: boolean\r\n  isLoading: boolean\r\n  error: string | null\r\n  token: string | null\r\n}\r\n\r\n// App UI state\r\nexport interface UIState {\r\n  sidebarOpen: boolean\r\n  theme: 'light' | 'dark' | 'system'\r\n  notifications: Notification[]\r\n  loadingStates: Record<string, boolean>\r\n  modals: Record<string, boolean>\r\n}\r\n\r\n// Notification type\r\nexport interface Notification {\r\n  id: string\r\n  type: 'success' | 'error' | 'warning' | 'info'\r\n  title: string\r\n  message: string\r\n  duration?: number\r\n  createdAt: Date\r\n}\r\n\r\n// App settings state\r\nexport interface SettingsState {\r\n  language: string\r\n  timezone: string\r\n  emailNotifications: boolean\r\n  pushNotifications: boolean\r\n  autoSave: boolean\r\n}\r\n\r\n// Navigation state\r\nexport interface NavigationState {\r\n  currentRoute: string\r\n  previousRoute: string\r\n  breadcrumbs: Breadcrumb[]\r\n}\r\n\r\nexport interface Breadcrumb {\r\n  label: string\r\n  href: string\r\n  active: boolean\r\n}\r\n\r\n// Form state\r\nexport interface FormState {\r\n  dirty: boolean\r\n  isValid: boolean\r\n  errors: Record<string, string>\r\n  touched: Record<string, boolean>\r\n}\r\n\r\n// Main app state interface\r\nexport interface AppState {\r\n  // Core states\r\n  auth: AuthState\r\n  ui: UIState\r\n  settings: SettingsState\r\n  navigation: NavigationState\r\n  \r\n  // Computed states\r\n  isOnline: boolean\r\n  isMobile: boolean\r\n  \r\n  // Form states\r\n  forms: Record<string, FormState>\r\n}\r\n\r\n// Initial state values\r\nexport const initialAuthState: AuthState = {\r\n  user: null,\r\n  isAuthenticated: false,\r\n  isLoading: false,\r\n  error: null,\r\n  token: null,\r\n}\r\n\r\nexport const initialUIState: UIState = {\r\n  sidebarOpen: false,\r\n  theme: 'system',\r\n  notifications: [],\r\n  loadingStates: {},\r\n  modals: {},\r\n}\r\n\r\nexport const initialSettingsState: SettingsState = {\r\n  language: 'en',\r\n  timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,\r\n  emailNotifications: true,\r\n  pushNotifications: true,\r\n  autoSave: true,\r\n}\r\n\r\nexport const initialNavigationState: NavigationState = {\r\n  currentRoute: '/',\r\n  previousRoute: '/',\r\n  breadcrumbs: [],\r\n}\r\n\r\nexport const initialAppState: AppState = {\r\n  auth: initialAuthState,\r\n  ui: initialUIState,\r\n  settings: initialSettingsState,\r\n  navigation: initialNavigationState,\r\n  isOnline: true, // Default to true, will be updated on client mount\r\n  isMobile: false,\r\n  forms: {},\r\n}"], "names": [], "mappings": "AAAA,aAAa;;;;;;;;AAwFN,MAAM,mBAA8B;IACzC,MAAM;IACN,iBAAiB;IACjB,WAAW;IACX,OAAO;IACP,OAAO;AACT;AAEO,MAAM,iBAA0B;IACrC,aAAa;IACb,OAAO;IACP,eAAe,EAAE;IACjB,eAAe,CAAC;IAChB,QAAQ,CAAC;AACX;AAEO,MAAM,uBAAsC;IACjD,UAAU;IACV,UAAU,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ;IAC1D,oBAAoB;IACpB,mBAAmB;IACnB,UAAU;AACZ;AAEO,MAAM,yBAA0C;IACrD,cAAc;IACd,eAAe;IACf,aAAa,EAAE;AACjB;AAEO,MAAM,kBAA4B;IACvC,MAAM;IACN,IAAI;IACJ,UAAU;IACV,YAAY;IACZ,UAAU;IACV,UAAU;IACV,OAAO,CAAC;AACV", "debugId": null}}, {"offset": {"line": 1527, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/hooks/context/useAppStore.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { create } from 'zustand'\r\nimport { subscribeWithSelector } from 'zustand/middleware'\r\nimport { AppState, initialAppState, Notification } from './useAppState'\r\n\r\n// Store interface combining state and actions\r\nexport interface AppStore extends AppState {\r\n  // Auth actions\r\n  setUser: (user: AppState['auth']['user']) => void\r\n  setAuthLoading: (loading: boolean) => void\r\n  setAuthError: (error: string | null) => void\r\n  setToken: (token: string | null) => void\r\n  logout: () => void\r\n  \r\n  // UI actions\r\n  toggleSidebar: () => void\r\n  setSidebarOpen: (open: boolean) => void\r\n  setTheme: (theme: AppState['ui']['theme']) => void\r\n  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => void\r\n  removeNotification: (id: string) => void\r\n  setLoadingState: (key: string, loading: boolean) => void\r\n  setModalState: (key: string, open: boolean) => void\r\n  \r\n  // Settings actions\r\n  updateSettings: (settings: Partial<AppState['settings']>) => void\r\n  \r\n  // Navigation actions\r\n  setCurrentRoute: (route: string) => void\r\n  setBreadcrumbs: (breadcrumbs: AppState['navigation']['breadcrumbs']) => void\r\n  \r\n  // Form actions\r\n  setFormState: (formId: string, formState: AppState['forms'][string]) => void\r\n  resetForm: (formId: string) => void\r\n  \r\n  // System actions\r\n  setOnlineStatus: (online: boolean) => void\r\n  setMobileStatus: (mobile: boolean) => void\r\n  \r\n  // Internal actions for initialization\r\n  _initializeFromState: (state: Partial<AppState>) => void\r\n  _reset: () => void\r\n}\r\n\r\n// Create the Zustand store with subscribeWithSelector middleware for fine-grained subscriptions\r\nexport const createAppStore = (initialState: AppState = initialAppState) =>\r\n  create<AppStore>()(\r\n    subscribeWithSelector((set, get) => ({\r\n      // Initial state\r\n      ...initialState,\r\n      \r\n      // Auth actions\r\n      setUser: (user) =>\r\n        set((state) => ({\r\n          auth: {\r\n            ...state.auth,\r\n            user,\r\n            isAuthenticated: !!user,\r\n          },\r\n        })),\r\n      \r\n      setAuthLoading: (loading) =>\r\n        set((state) => ({\r\n          auth: {\r\n            ...state.auth,\r\n            isLoading: loading,\r\n          },\r\n        })),\r\n      \r\n      setAuthError: (error) =>\r\n        set((state) => ({\r\n          auth: {\r\n            ...state.auth,\r\n            error,\r\n          },\r\n        })),\r\n      \r\n      setToken: (token) =>\r\n        set((state) => ({\r\n          auth: {\r\n            ...state.auth,\r\n            token,\r\n          },\r\n        })),\r\n      \r\n      logout: () =>\r\n        set((state) => ({\r\n          auth: {\r\n            user: null,\r\n            isAuthenticated: false,\r\n            isLoading: false,\r\n            error: null,\r\n            token: null,\r\n          },\r\n        })),\r\n      \r\n      // UI actions\r\n      toggleSidebar: () =>\r\n        set((state) => ({\r\n          ui: {\r\n            ...state.ui,\r\n            sidebarOpen: !state.ui.sidebarOpen,\r\n          },\r\n        })),\r\n      \r\n      setSidebarOpen: (open) =>\r\n        set((state) => ({\r\n          ui: {\r\n            ...state.ui,\r\n            sidebarOpen: open,\r\n          },\r\n        })),\r\n      \r\n      setTheme: (theme) =>\r\n        set((state) => ({\r\n          ui: {\r\n            ...state.ui,\r\n            theme,\r\n          },\r\n        })),\r\n      \r\n      addNotification: (notification) =>\r\n        set((state) => {\r\n          const newNotification: Notification = {\r\n            ...notification,\r\n            id: Date.now().toString(),\r\n            createdAt: new Date(),\r\n          }\r\n          return {\r\n            ui: {\r\n              ...state.ui,\r\n              notifications: [...state.ui.notifications, newNotification],\r\n            },\r\n          }\r\n        }),\r\n      \r\n      removeNotification: (id) =>\r\n        set((state) => ({\r\n          ui: {\r\n            ...state.ui,\r\n            notifications: state.ui.notifications.filter((n) => n.id !== id),\r\n          },\r\n        })),\r\n      \r\n      setLoadingState: (key, loading) =>\r\n        set((state) => ({\r\n          ui: {\r\n            ...state.ui,\r\n            loadingStates: {\r\n              ...state.ui.loadingStates,\r\n              [key]: loading,\r\n            },\r\n          },\r\n        })),\r\n      \r\n      setModalState: (key, open) =>\r\n        set((state) => ({\r\n          ui: {\r\n            ...state.ui,\r\n            modals: {\r\n              ...state.ui.modals,\r\n              [key]: open,\r\n            },\r\n          },\r\n        })),\r\n      \r\n      // Settings actions\r\n      updateSettings: (settings) =>\r\n        set((state) => ({\r\n          settings: {\r\n            ...state.settings,\r\n            ...settings,\r\n          },\r\n        })),\r\n      \r\n      // Navigation actions\r\n      setCurrentRoute: (route) =>\r\n        set((state) => ({\r\n          navigation: {\r\n            ...state.navigation,\r\n            previousRoute: state.navigation.currentRoute,\r\n            currentRoute: route,\r\n          },\r\n        })),\r\n      \r\n      setBreadcrumbs: (breadcrumbs) =>\r\n        set((state) => ({\r\n          navigation: {\r\n            ...state.navigation,\r\n            breadcrumbs,\r\n          },\r\n        })),\r\n      \r\n      // Form actions\r\n      setFormState: (formId, formState) =>\r\n        set((state) => ({\r\n          forms: {\r\n            ...state.forms,\r\n            [formId]: formState,\r\n          },\r\n        })),\r\n      \r\n      resetForm: (formId) =>\r\n        set((state) => {\r\n          const { [formId]: removed, ...remainingForms } = state.forms\r\n          return {\r\n            forms: remainingForms,\r\n          }\r\n        }),\r\n      \r\n      // System actions\r\n      setOnlineStatus: (online) =>\r\n        set(() => ({\r\n          isOnline: online,\r\n        })),\r\n      \r\n      setMobileStatus: (mobile) =>\r\n        set(() => ({\r\n          isMobile: mobile,\r\n        })),\r\n      \r\n      // Internal actions\r\n      _initializeFromState: (state) =>\r\n        set((currentState) => ({\r\n          ...currentState,\r\n          ...state,\r\n        })),\r\n      \r\n      _reset: () => set(initialState),\r\n    }))\r\n  )\r\n\r\n// Default store instance (can be overridden by context)\r\nexport const useAppStore = createAppStore()"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;AA6CO,MAAM,iBAAiB,CAAC,eAAyB,uIAAA,CAAA,kBAAe,GACrE,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IACH,CAAA,GAAA,6IAAA,CAAA,wBAAqB,AAAD,EAAE,CAAC,KAAK,MAAQ,CAAC;YACnC,gBAAgB;YAChB,GAAG,YAAY;YAEf,eAAe;YACf,SAAS,CAAC,OACR,IAAI,CAAC,QAAU,CAAC;wBACd,MAAM;4BACJ,GAAG,MAAM,IAAI;4BACb;4BACA,iBAAiB,CAAC,CAAC;wBACrB;oBACF,CAAC;YAEH,gBAAgB,CAAC,UACf,IAAI,CAAC,QAAU,CAAC;wBACd,MAAM;4BACJ,GAAG,MAAM,IAAI;4BACb,WAAW;wBACb;oBACF,CAAC;YAEH,cAAc,CAAC,QACb,IAAI,CAAC,QAAU,CAAC;wBACd,MAAM;4BACJ,GAAG,MAAM,IAAI;4BACb;wBACF;oBACF,CAAC;YAEH,UAAU,CAAC,QACT,IAAI,CAAC,QAAU,CAAC;wBACd,MAAM;4BACJ,GAAG,MAAM,IAAI;4BACb;wBACF;oBACF,CAAC;YAEH,QAAQ,IACN,IAAI,CAAC,QAAU,CAAC;wBACd,MAAM;4BACJ,MAAM;4BACN,iBAAiB;4BACjB,WAAW;4BACX,OAAO;4BACP,OAAO;wBACT;oBACF,CAAC;YAEH,aAAa;YACb,eAAe,IACb,IAAI,CAAC,QAAU,CAAC;wBACd,IAAI;4BACF,GAAG,MAAM,EAAE;4BACX,aAAa,CAAC,MAAM,EAAE,CAAC,WAAW;wBACpC;oBACF,CAAC;YAEH,gBAAgB,CAAC,OACf,IAAI,CAAC,QAAU,CAAC;wBACd,IAAI;4BACF,GAAG,MAAM,EAAE;4BACX,aAAa;wBACf;oBACF,CAAC;YAEH,UAAU,CAAC,QACT,IAAI,CAAC,QAAU,CAAC;wBACd,IAAI;4BACF,GAAG,MAAM,EAAE;4BACX;wBACF;oBACF,CAAC;YAEH,iBAAiB,CAAC,eAChB,IAAI,CAAC;oBACH,MAAM,kBAAgC;wBACpC,GAAG,YAAY;wBACf,IAAI,KAAK,GAAG,GAAG,QAAQ;wBACvB,WAAW,IAAI;oBACjB;oBACA,OAAO;wBACL,IAAI;4BACF,GAAG,MAAM,EAAE;4BACX,eAAe;mCAAI,MAAM,EAAE,CAAC,aAAa;gCAAE;6BAAgB;wBAC7D;oBACF;gBACF;YAEF,oBAAoB,CAAC,KACnB,IAAI,CAAC,QAAU,CAAC;wBACd,IAAI;4BACF,GAAG,MAAM,EAAE;4BACX,eAAe,MAAM,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;wBAC/D;oBACF,CAAC;YAEH,iBAAiB,CAAC,KAAK,UACrB,IAAI,CAAC,QAAU,CAAC;wBACd,IAAI;4BACF,GAAG,MAAM,EAAE;4BACX,eAAe;gCACb,GAAG,MAAM,EAAE,CAAC,aAAa;gCACzB,CAAC,IAAI,EAAE;4BACT;wBACF;oBACF,CAAC;YAEH,eAAe,CAAC,KAAK,OACnB,IAAI,CAAC,QAAU,CAAC;wBACd,IAAI;4BACF,GAAG,MAAM,EAAE;4BACX,QAAQ;gCACN,GAAG,MAAM,EAAE,CAAC,MAAM;gCAClB,CAAC,IAAI,EAAE;4BACT;wBACF;oBACF,CAAC;YAEH,mBAAmB;YACnB,gBAAgB,CAAC,WACf,IAAI,CAAC,QAAU,CAAC;wBACd,UAAU;4BACR,GAAG,MAAM,QAAQ;4BACjB,GAAG,QAAQ;wBACb;oBACF,CAAC;YAEH,qBAAqB;YACrB,iBAAiB,CAAC,QAChB,IAAI,CAAC,QAAU,CAAC;wBACd,YAAY;4BACV,GAAG,MAAM,UAAU;4BACnB,eAAe,MAAM,UAAU,CAAC,YAAY;4BAC5C,cAAc;wBAChB;oBACF,CAAC;YAEH,gBAAgB,CAAC,cACf,IAAI,CAAC,QAAU,CAAC;wBACd,YAAY;4BACV,GAAG,MAAM,UAAU;4BACnB;wBACF;oBACF,CAAC;YAEH,eAAe;YACf,cAAc,CAAC,QAAQ,YACrB,IAAI,CAAC,QAAU,CAAC;wBACd,OAAO;4BACL,GAAG,MAAM,KAAK;4BACd,CAAC,OAAO,EAAE;wBACZ;oBACF,CAAC;YAEH,WAAW,CAAC,SACV,IAAI,CAAC;oBACH,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,gBAAgB,GAAG,MAAM,KAAK;oBAC5D,OAAO;wBACL,OAAO;oBACT;gBACF;YAEF,iBAAiB;YACjB,iBAAiB,CAAC,SAChB,IAAI,IAAM,CAAC;wBACT,UAAU;oBACZ,CAAC;YAEH,iBAAiB,CAAC,SAChB,IAAI,IAAM,CAAC;wBACT,UAAU;oBACZ,CAAC;YAEH,mBAAmB;YACnB,sBAAsB,CAAC,QACrB,IAAI,CAAC,eAAiB,CAAC;wBACrB,GAAG,YAAY;wBACf,GAAG,KAAK;oBACV,CAAC;YAEH,QAAQ,IAAM,IAAI;QACpB,CAAC;AAIE,MAAM,cAAc", "debugId": null}}, {"offset": {"line": 1688, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/hooks/context/useAppContext.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React, { createContext, useContext, useEffect, ReactNode, useRef } from 'react'\r\nimport { useStore } from 'zustand'\r\nimport { AppState, initialAppState } from './useAppState'\r\nimport { AppStore, createAppStore } from './useAppStore'\r\n\r\n// Context to provide the store instance\r\nconst AppStoreContext = createContext<ReturnType<typeof createAppStore> | null>(null)\r\n\r\n// Provider props\r\ninterface AppProviderProps {\r\n  children: ReactNode\r\n  initialState?: Partial<AppState>\r\n}\r\n\r\n// Provider component that combines Zustand with Context API\r\nexport function AppProvider({ children, initialState }: AppProviderProps) {\r\n  // Create store instance only once per provider\r\n  const storeRef = useRef<ReturnType<typeof createAppStore>>()\r\n  \r\n  if (!storeRef.current) {\r\n    const mergedInitialState = { ...initialAppState, ...initialState }\r\n    storeRef.current = createAppStore(mergedInitialState)\r\n  }\r\n\r\n  const store = storeRef.current\r\n\r\n  // Initialize app state on mount (SSR-safe) and attach listeners\r\n  useEffect(() => {\r\n    if (typeof window === 'undefined') return\r\n\r\n    let resizeRaf: number | null = null\r\n\r\n    const checkMobile = () => {\r\n      const nextIsMobile = window.innerWidth < 768\r\n      const currentIsMobile = store.getState().isMobile\r\n      if (nextIsMobile !== currentIsMobile) {\r\n        store.getState().setMobileStatus(nextIsMobile)\r\n      }\r\n    }\r\n\r\n    const onResize = () => {\r\n      if (resizeRaf !== null) cancelAnimationFrame(resizeRaf)\r\n      resizeRaf = requestAnimationFrame(checkMobile)\r\n    }\r\n\r\n    // Online/offline handlers\r\n    const handleOnline = () => store.getState().setOnlineStatus(true)\r\n    const handleOffline = () => store.getState().setOnlineStatus(false)\r\n\r\n    // Initial setup\r\n    checkMobile()\r\n    \r\n    if (typeof navigator !== 'undefined' && typeof navigator.onLine === 'boolean') {\r\n      store.getState().setOnlineStatus(navigator.onLine)\r\n    }\r\n\r\n    window.addEventListener('resize', onResize, { passive: true })\r\n    window.addEventListener('online', handleOnline)\r\n    window.addEventListener('offline', handleOffline)\r\n\r\n    // Load theme and settings from localStorage\r\n    try {\r\n      const savedTheme = window.localStorage?.getItem('theme') as AppState['ui']['theme']\r\n      if (savedTheme && savedTheme !== store.getState().ui.theme) {\r\n        store.getState().setTheme(savedTheme)\r\n      }\r\n    } catch (e) {\r\n      console.warn('Theme localStorage read failed:', e)\r\n    }\r\n\r\n    try {\r\n      const savedSettings = window.localStorage?.getItem('app-settings')\r\n      if (savedSettings) {\r\n        const parsed = JSON.parse(savedSettings)\r\n        const currentSettings = store.getState().settings\r\n        const hasDiff = Object.keys(parsed).some(\r\n          (k) => (parsed as any)[k] !== (currentSettings as any)[k]\r\n        )\r\n        if (hasDiff) {\r\n          store.getState().updateSettings(parsed)\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.warn('Failed to load settings from localStorage:', error)\r\n    }\r\n\r\n    return () => {\r\n      if (resizeRaf !== null) cancelAnimationFrame(resizeRaf)\r\n      window.removeEventListener('resize', onResize)\r\n      window.removeEventListener('online', handleOnline)\r\n      window.removeEventListener('offline', handleOffline)\r\n    }\r\n  }, [store])\r\n\r\n  // Persist theme changes to localStorage\r\n  useEffect(() => {\r\n    if (typeof window === 'undefined') return\r\n    \r\n    const unsubscribe = store.subscribe(\r\n      (state) => state.ui.theme,\r\n      (theme) => {\r\n        try {\r\n          const current = window.localStorage.getItem('theme')\r\n          if (current !== theme) {\r\n            window.localStorage.setItem('theme', theme)\r\n          }\r\n        } catch {}\r\n      }\r\n    )\r\n\r\n    return unsubscribe\r\n  }, [store])\r\n\r\n  // Persist settings changes to localStorage\r\n  useEffect(() => {\r\n    if (typeof window === 'undefined') return\r\n    \r\n    const unsubscribe = store.subscribe(\r\n      (state) => state.settings,\r\n      (settings) => {\r\n        try {\r\n          const existing = window.localStorage.getItem('app-settings')\r\n          const next = JSON.stringify(settings)\r\n          if (existing !== next) {\r\n            window.localStorage.setItem('app-settings', next)\r\n          }\r\n        } catch {}\r\n      }\r\n    )\r\n\r\n    return unsubscribe\r\n  }, [store])\r\n\r\n  // Auto-remove notifications after duration\r\n  useEffect(() => {\r\n    const unsubscribe = store.subscribe(\r\n      (state) => state.ui.notifications,\r\n      (notifications) => {\r\n        notifications.forEach((notification) => {\r\n          if (notification.duration) {\r\n            setTimeout(() => {\r\n              // Check if notification still exists before removing\r\n              const currentNotifications = store.getState().ui.notifications\r\n              if (currentNotifications.find(n => n.id === notification.id)) {\r\n                store.getState().removeNotification(notification.id)\r\n              }\r\n            }, notification.duration)\r\n          }\r\n        })\r\n      }\r\n    )\r\n\r\n    return unsubscribe\r\n  }, [store])\r\n\r\n  return (\r\n    <AppStoreContext.Provider value={store}>\r\n      {children}\r\n    </AppStoreContext.Provider>\r\n  )\r\n}\r\n\r\n// Hook to get the store instance from context\r\nfunction useAppStoreContext() {\r\n  const store = useContext(AppStoreContext)\r\n  if (!store) {\r\n    throw new Error('useAppStoreContext must be used within an AppProvider')\r\n  }\r\n  return store\r\n}\r\n\r\n// Main hook to use the app store (with context support)\r\nexport function useAppContext() {\r\n  const store = useAppStoreContext()\r\n  return useStore(store)\r\n}\r\n\r\n// Convenience hooks for specific state slices with optimized selectors\r\nexport function useAuth() {\r\n  const store = useAppStoreContext()\r\n  \r\n  // Subscribe only to auth-related state\r\n  const authState = useStore(store, (state) => state.auth)\r\n  const setUser = useStore(store, (state) => state.setUser)\r\n  const setAuthLoading = useStore(store, (state) => state.setAuthLoading)\r\n  const setAuthError = useStore(store, (state) => state.setAuthError)\r\n  const setToken = useStore(store, (state) => state.setToken)\r\n  const logout = useStore(store, (state) => state.logout)\r\n\r\n  return {\r\n    user: authState.user,\r\n    isAuthenticated: authState.isAuthenticated,\r\n    isLoading: authState.isLoading,\r\n    error: authState.error,\r\n    token: authState.token,\r\n    setUser,\r\n    setAuthLoading,\r\n    setAuthError,\r\n    setToken,\r\n    logout,\r\n  }\r\n}\r\n\r\nexport function useUI() {\r\n  const store = useAppStoreContext()\r\n  \r\n  const uiState = useStore(store, (state) => state.ui)\r\n  const toggleSidebar = useStore(store, (state) => state.toggleSidebar)\r\n  const setSidebarOpen = useStore(store, (state) => state.setSidebarOpen)\r\n  const setTheme = useStore(store, (state) => state.setTheme)\r\n  const addNotification = useStore(store, (state) => state.addNotification)\r\n  const removeNotification = useStore(store, (state) => state.removeNotification)\r\n  const setLoadingState = useStore(store, (state) => state.setLoadingState)\r\n  const setModalState = useStore(store, (state) => state.setModalState)\r\n\r\n  return {\r\n    sidebarOpen: uiState.sidebarOpen,\r\n    theme: uiState.theme,\r\n    notifications: uiState.notifications,\r\n    loadingStates: uiState.loadingStates,\r\n    modals: uiState.modals,\r\n    toggleSidebar,\r\n    setSidebarOpen,\r\n    setTheme,\r\n    addNotification,\r\n    removeNotification,\r\n    setLoadingState,\r\n    setModalState,\r\n  }\r\n}\r\n\r\nexport function useSettings() {\r\n  const store = useAppStoreContext()\r\n  \r\n  const settings = useStore(store, (state) => state.settings)\r\n  const updateSettings = useStore(store, (state) => state.updateSettings)\r\n\r\n  return {\r\n    settings,\r\n    updateSettings,\r\n  }\r\n}\r\n\r\nexport function useNavigation() {\r\n  const store = useAppStoreContext()\r\n  \r\n  const navigationState = useStore(store, (state) => state.navigation)\r\n  const setCurrentRoute = useStore(store, (state) => state.setCurrentRoute)\r\n  const setBreadcrumbs = useStore(store, (state) => state.setBreadcrumbs)\r\n\r\n  return {\r\n    currentRoute: navigationState.currentRoute,\r\n    previousRoute: navigationState.previousRoute,\r\n    breadcrumbs: navigationState.breadcrumbs,\r\n    setCurrentRoute,\r\n    setBreadcrumbs,\r\n  }\r\n}\r\n\r\nexport function useForms() {\r\n  const store = useAppStoreContext()\r\n  \r\n  const forms = useStore(store, (state) => state.forms)\r\n  const setFormState = useStore(store, (state) => state.setFormState)\r\n  const resetForm = useStore(store, (state) => state.resetForm)\r\n\r\n  return {\r\n    forms,\r\n    setFormState,\r\n    resetForm,\r\n  }\r\n}\r\n\r\nexport function useSystem() {\r\n  const store = useAppStoreContext()\r\n  \r\n  const isOnline = useStore(store, (state) => state.isOnline)\r\n  const isMobile = useStore(store, (state) => state.isMobile)\r\n  const setOnlineStatus = useStore(store, (state) => state.setOnlineStatus)\r\n  const setMobileStatus = useStore(store, (state) => state.setMobileStatus)\r\n\r\n  return {\r\n    isOnline,\r\n    isMobile,\r\n    setOnlineStatus,\r\n    setMobileStatus,\r\n  }\r\n}\r\n\r\n// Advanced hooks for specific use cases\r\n\r\n// Hook for subscribing to specific loading states\r\nexport function useLoadingState(key: string) {\r\n  const store = useAppStoreContext()\r\n  return useStore(store, (state) => state.ui.loadingStates[key] || false)\r\n}\r\n\r\n// Hook for subscribing to specific modal states\r\nexport function useModalState(key: string) {\r\n  const store = useAppStoreContext()\r\n  const isOpen = useStore(store, (state) => state.ui.modals[key] || false)\r\n  const setModalState = useStore(store, (state) => state.setModalState)\r\n  \r\n  return {\r\n    isOpen,\r\n    open: () => setModalState(key, true),\r\n    close: () => setModalState(key, false),\r\n    toggle: () => setModalState(key, !isOpen),\r\n  }\r\n}\r\n\r\n// Hook for subscribing to specific form states\r\nexport function useFormState(formId: string) {\r\n  const store = useAppStoreContext()\r\n  const formState = useStore(store, (state) => state.forms[formId])\r\n  const setFormState = useStore(store, (state) => state.setFormState)\r\n  const resetForm = useStore(store, (state) => state.resetForm)\r\n  \r\n  return {\r\n    formState,\r\n    setFormState: (state: AppState['forms'][string]) => setFormState(formId, state),\r\n    resetForm: () => resetForm(formId),\r\n  }\r\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,wCAAwC;AACxC,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA4C;AASzE,SAAS,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAoB;IACtE,+CAA+C;IAC/C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAEtB,IAAI,CAAC,SAAS,OAAO,EAAE;QACrB,MAAM,qBAAqB;YAAE,GAAG,uIAAA,CAAA,kBAAe;YAAE,GAAG,YAAY;QAAC;QACjE,SAAS,OAAO,GAAG,CAAA,GAAA,uIAAA,CAAA,iBAAc,AAAD,EAAE;IACpC;IAEA,MAAM,QAAQ,SAAS,OAAO;IAE9B,gEAAgE;IAChE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC;;;QAEnC,IAAI;QAEJ,MAAM;QAQN,MAAM;QAKN,0BAA0B;QAC1B,MAAM;QACN,MAAM;IA6CR,GAAG;QAAC;KAAM;IAEV,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC;;;QAEnC,MAAM;IAaR,GAAG;QAAC;KAAM;IAEV,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC;;;QAEnC,MAAM;IAcR,GAAG;QAAC;KAAM;IAEV,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,MAAM,SAAS,CACjC,CAAC,QAAU,MAAM,EAAE,CAAC,aAAa,EACjC,CAAC;YACC,cAAc,OAAO,CAAC,CAAC;gBACrB,IAAI,aAAa,QAAQ,EAAE;oBACzB,WAAW;wBACT,qDAAqD;wBACrD,MAAM,uBAAuB,MAAM,QAAQ,GAAG,EAAE,CAAC,aAAa;wBAC9D,IAAI,qBAAqB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,EAAE,GAAG;4BAC5D,MAAM,QAAQ,GAAG,kBAAkB,CAAC,aAAa,EAAE;wBACrD;oBACF,GAAG,aAAa,QAAQ;gBAC1B;YACF;QACF;QAGF,OAAO;IACT,GAAG;QAAC;KAAM;IAEV,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;kBAC9B;;;;;;AAGP;AAEA,8CAA8C;AAC9C,SAAS;IACP,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IACzB,IAAI,CAAC,OAAO;QACV,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,QAAQ;IACd,OAAO,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE;AAClB;AAGO,SAAS;IACd,MAAM,QAAQ;IAEd,uCAAuC;IACvC,MAAM,YAAY,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,IAAI;IACvD,MAAM,UAAU,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,OAAO;IACxD,MAAM,iBAAiB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,cAAc;IACtE,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,YAAY;IAClE,MAAM,WAAW,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,QAAQ;IAC1D,MAAM,SAAS,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,MAAM;IAEtD,OAAO;QACL,MAAM,UAAU,IAAI;QACpB,iBAAiB,UAAU,eAAe;QAC1C,WAAW,UAAU,SAAS;QAC9B,OAAO,UAAU,KAAK;QACtB,OAAO,UAAU,KAAK;QACtB;QACA;QACA;QACA;QACA;IACF;AACF;AAEO,SAAS;IACd,MAAM,QAAQ;IAEd,MAAM,UAAU,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,EAAE;IACnD,MAAM,gBAAgB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,aAAa;IACpE,MAAM,iBAAiB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,cAAc;IACtE,MAAM,WAAW,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,QAAQ;IAC1D,MAAM,kBAAkB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,eAAe;IACxE,MAAM,qBAAqB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,kBAAkB;IAC9E,MAAM,kBAAkB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,eAAe;IACxE,MAAM,gBAAgB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,aAAa;IAEpE,OAAO;QACL,aAAa,QAAQ,WAAW;QAChC,OAAO,QAAQ,KAAK;QACpB,eAAe,QAAQ,aAAa;QACpC,eAAe,QAAQ,aAAa;QACpC,QAAQ,QAAQ,MAAM;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAEO,SAAS;IACd,MAAM,QAAQ;IAEd,MAAM,WAAW,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,QAAQ;IAC1D,MAAM,iBAAiB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,cAAc;IAEtE,OAAO;QACL;QACA;IACF;AACF;AAEO,SAAS;IACd,MAAM,QAAQ;IAEd,MAAM,kBAAkB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,UAAU;IACnE,MAAM,kBAAkB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,eAAe;IACxE,MAAM,iBAAiB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,cAAc;IAEtE,OAAO;QACL,cAAc,gBAAgB,YAAY;QAC1C,eAAe,gBAAgB,aAAa;QAC5C,aAAa,gBAAgB,WAAW;QACxC;QACA;IACF;AACF;AAEO,SAAS;IACd,MAAM,QAAQ;IAEd,MAAM,QAAQ,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,KAAK;IACpD,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,YAAY;IAClE,MAAM,YAAY,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,SAAS;IAE5D,OAAO;QACL;QACA;QACA;IACF;AACF;AAEO,SAAS;IACd,MAAM,QAAQ;IAEd,MAAM,WAAW,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,QAAQ;IAC1D,MAAM,WAAW,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,QAAQ;IAC1D,MAAM,kBAAkB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,eAAe;IACxE,MAAM,kBAAkB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,eAAe;IAExE,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAKO,SAAS,gBAAgB,GAAW;IACzC,MAAM,QAAQ;IACd,OAAO,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,EAAE,CAAC,aAAa,CAAC,IAAI,IAAI;AACnE;AAGO,SAAS,cAAc,GAAW;IACvC,MAAM,QAAQ;IACd,MAAM,SAAS,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,EAAE,CAAC,MAAM,CAAC,IAAI,IAAI;IAClE,MAAM,gBAAgB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,aAAa;IAEpE,OAAO;QACL;QACA,MAAM,IAAM,cAAc,KAAK;QAC/B,OAAO,IAAM,cAAc,KAAK;QAChC,QAAQ,IAAM,cAAc,KAAK,CAAC;IACpC;AACF;AAGO,SAAS,aAAa,MAAc;IACzC,MAAM,QAAQ;IACd,MAAM,YAAY,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,KAAK,CAAC,OAAO;IAChE,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,YAAY;IAClE,MAAM,YAAY,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,QAAU,MAAM,SAAS;IAE5D,OAAO;QACL;QACA,cAAc,CAAC,QAAqC,aAAa,QAAQ;QACzE,WAAW,IAAM,UAAU;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 1920, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/status-indicator.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useEffect, useState } from 'react'\r\n\r\ninterface StatusIndicatorProps {\r\n  isOnline: boolean\r\n  isMobile: boolean\r\n}\r\n\r\nexport function StatusIndicator({ isOnline, isMobile }: StatusIndicatorProps) {\r\n  const [mounted, setMounted] = useState(false)\r\n\r\n  useEffect(() => {\r\n    setMounted(true)\r\n  }, [])\r\n\r\n  // Don't render anything until mounted to avoid hydration mismatch\r\n  if (!mounted) {\r\n    return (\r\n      <div className=\"flex items-center gap-2\">\r\n        <span className=\"text-xs px-2 py-0.5 rounded bg-gray-200\">Loading...</span>\r\n        <span className=\"text-xs px-2 py-0.5 rounded bg-gray-200\">Loading...</span>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <span className=\"text-xs px-2 py-0.5 rounded bg-gray-200\">\r\n        {isMobile ? 'Mobile' : 'Desktop'}\r\n      </span>\r\n      <span \r\n        className={`text-xs px-2 py-0.5 rounded ${\r\n          isOnline ? 'bg-green-200' : 'bg-red-200'\r\n        }`}\r\n      >\r\n        {isOnline ? 'Online' : 'Offline'}\r\n      </span>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASO,SAAS,gBAAgB,EAAE,QAAQ,EAAE,QAAQ,EAAwB;IAC1E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,kEAAkE;IAClE,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAK,WAAU;8BAA0C;;;;;;8BAC1D,8OAAC;oBAAK,WAAU;8BAA0C;;;;;;;;;;;;IAGhE;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAK,WAAU;0BACb,WAAW,WAAW;;;;;;0BAEzB,8OAAC;gBACC,WAAW,CAAC,4BAA4B,EACtC,WAAW,iBAAiB,cAC5B;0BAED,WAAW,WAAW;;;;;;;;;;;;AAI/B", "debugId": null}}, {"offset": {"line": 1991, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/hooks/context/contextDemo.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React from 'react'\r\nimport { useAppContext, useAuth, useUI, useSettings, useNavigation, useForms, useSystem, useLoadingState, useModalState, useFormState } from './useAppContext'\r\nimport { StatusIndicator } from '@/components/ui/status-indicator'\r\n\r\n// Development-only floating panel to inspect and interact with app context\r\nexport function ContextDemo() {\r\n  // Using the main context hook to get full state access\r\n  const state = useAppContext()\r\n  \r\n  // Using specialized hooks for specific functionality\r\n  const { user, isAuthenticated, setUser, logout } = useAuth()\r\n  const { theme, sidebarOpen, notifications, setTheme, toggleSidebar, addNotification } = useUI()\r\n  const { settings, updateSettings } = useSettings()\r\n  const { currentRoute, setCurrentRoute } = useNavigation()\r\n  const { forms } = useForms()\r\n  const { isOnline, isMobile } = useSystem()\r\n  \r\n  // Using advanced hooks for specific states\r\n  const exampleLoading = useLoadingState('example-action')\r\n  const { isOpen: exampleModalOpen, open: openExampleModal, close: closeExampleModal, toggle: toggleExampleModal } = useModalState('example-modal')\r\n  const { formState: exampleFormState, setFormState: setExampleFormState, resetForm: resetExampleForm } = useFormState('example-form')\r\n\r\n  const handleLogin = () => {\r\n    const mockUser = {\r\n      id: '1',\r\n      email: '<EMAIL>',\r\n      firstName: 'John',\r\n      lastName: 'Doe',\r\n      role: 'patient' as const,\r\n      isEmailVerified: true,\r\n      createdAt: new Date().toISOString(),\r\n      updatedAt: new Date().toISOString(),\r\n    }\r\n    setUser(mockUser)\r\n    addNotification({\r\n      type: 'success',\r\n      title: 'Welcome!',\r\n      message: 'Successfully logged in with Zustand + Context',\r\n      duration: 3000,\r\n    })\r\n  }\r\n\r\n  const handleLogout = () => {\r\n    logout()\r\n    addNotification({\r\n      type: 'info',\r\n      title: 'Goodbye!',\r\n      message: 'You have been logged out',\r\n      duration: 3000,\r\n    })\r\n  }\r\n\r\n  const handleThemeChange = () => {\r\n    const next = theme === 'light' ? 'dark' : theme === 'dark' ? 'system' : 'light'\r\n    setTheme(next)\r\n    addNotification({\r\n      type: 'info',\r\n      title: 'Theme Changed',\r\n      message: `Switched to ${next} theme`,\r\n      duration: 2000,\r\n    })\r\n  }\r\n\r\n  const handleFormExample = () => {\r\n    setExampleFormState({\r\n      dirty: true,\r\n      isValid: false,\r\n      errors: { email: 'Invalid email format' },\r\n      touched: { email: true },\r\n    })\r\n  }\r\n\r\n  const handleAsyncAction = async () => {\r\n    state.setLoadingState('example-action', true)\r\n    \r\n    try {\r\n      // Simulate API call\r\n      await new Promise(resolve => setTimeout(resolve, 2000))\r\n      \r\n      addNotification({\r\n        type: 'success',\r\n        title: 'Success!',\r\n        message: 'Async action completed successfully',\r\n        duration: 5000,\r\n      })\r\n    } catch (error) {\r\n      addNotification({\r\n        type: 'error',\r\n        title: 'Error!',\r\n        message: 'Something went wrong',\r\n        duration: 5000,\r\n      })\r\n    } finally {\r\n      state.setLoadingState('example-action', false)\r\n    }\r\n  }\r\n\r\n  // Floating debug panel pinned bottom-right to ensure visibility\r\n  return (\r\n    <div className=\"fixed bottom-4 right-4 z-50 w-[380px] max-h-[85vh] overflow-auto rounded-lg border bg-white shadow-xl text-sm\">\r\n      <div className=\"flex items-center justify-between px-3 py-2 border-b bg-gray-50\">\r\n        <h1 className=\"font-semibold\">Zustand + Context Demo</h1>\r\n        <StatusIndicator isOnline={isOnline} isMobile={isMobile} />\r\n      </div>\r\n\r\n      <div className=\"p-3 space-y-3\">\r\n        {/* Auth */}\r\n        <section className=\"space-y-2\">\r\n          <div className=\"font-medium text-blue-700\">Auth</div>\r\n          <p>Status: {isAuthenticated ? 'Logged In' : 'Not Logged In'}</p>\r\n          {user && (\r\n            <div className=\"text-xs text-gray-700\">\r\n              <div>{user.firstName} {user.lastName} • {user.email}</div>\r\n              <div>Role: {user.role}</div>\r\n            </div>\r\n          )}\r\n          <div className=\"flex gap-2\">\r\n            <button onClick={handleLogin} className=\"px-2 py-1 rounded bg-blue-500 text-white hover:bg-blue-600 text-xs\">Login</button>\r\n            <button onClick={handleLogout} className=\"px-2 py-1 rounded bg-red-500 text-white hover:bg-red-600 text-xs\">Logout</button>\r\n          </div>\r\n        </section>\r\n\r\n        {/* UI */}\r\n        <section className=\"space-y-2\">\r\n          <div className=\"font-medium text-green-700\">UI</div>\r\n          <div className=\"flex items-center gap-2\">\r\n            <span className=\"text-xs\">Theme: {theme}</span>\r\n            <button onClick={handleThemeChange} className=\"px-2 py-1 rounded bg-gray-800 text-white hover:bg-gray-900 text-xs\">Toggle Theme</button>\r\n          </div>\r\n          <div className=\"flex items-center gap-2\">\r\n            <span className=\"text-xs\">Sidebar: {sidebarOpen ? 'Open' : 'Closed'}</span>\r\n            <button onClick={toggleSidebar} className=\"px-2 py-1 rounded bg-green-600 text-white hover:bg-green-700 text-xs\">Toggle Sidebar</button>\r\n          </div>\r\n          <div className=\"text-xs\">Notifications: {notifications.length}</div>\r\n          \r\n          {/* Modal Example */}\r\n          <div className=\"flex items-center gap-2\">\r\n            <span className=\"text-xs\">Example Modal: {exampleModalOpen ? 'Open' : 'Closed'}</span>\r\n            <button onClick={toggleExampleModal} className=\"px-2 py-1 rounded bg-indigo-600 text-white hover:bg-indigo-700 text-xs\">Toggle Modal</button>\r\n          </div>\r\n        </section>\r\n\r\n        {/* Settings */}\r\n        <section className=\"space-y-2\">\r\n          <div className=\"font-medium text-purple-700\">Settings</div>\r\n          <div className=\"flex items-center gap-2\">\r\n            <span className=\"text-xs\">Email Notifications: {settings.emailNotifications ? 'On' : 'Off'}</span>\r\n            <button\r\n              onClick={() => updateSettings({ emailNotifications: !settings.emailNotifications })}\r\n              className=\"px-2 py-1 rounded bg-purple-600 text-white hover:bg-purple-700 text-xs\"\r\n            >\r\n              Toggle Email\r\n            </button>\r\n          </div>\r\n          <div className=\"flex items-center gap-2\">\r\n            <span className=\"text-xs\">Auto Save: {settings.autoSave ? 'On' : 'Off'}</span>\r\n            <button\r\n              onClick={() => updateSettings({ autoSave: !settings.autoSave })}\r\n              className=\"px-2 py-1 rounded bg-purple-600 text-white hover:bg-purple-700 text-xs\"\r\n            >\r\n              Toggle Auto Save\r\n            </button>\r\n          </div>\r\n        </section>\r\n\r\n        {/* Navigation */}\r\n        <section className=\"space-y-2\">\r\n          <div className=\"font-medium text-orange-700\">Navigation</div>\r\n          <div className=\"flex items-center gap-2\">\r\n            <span className=\"truncate text-xs\" title={currentRoute}>Current: {currentRoute}</span>\r\n            <button\r\n              onClick={() => setCurrentRoute('/dashboard')}\r\n              className=\"px-2 py-1 rounded bg-orange-600 text-white hover:bg-orange-700 text-xs\"\r\n            >\r\n              To /dashboard\r\n            </button>\r\n          </div>\r\n        </section>\r\n\r\n        {/* Forms */}\r\n        <section className=\"space-y-2\">\r\n          <div className=\"font-medium text-teal-700\">Forms</div>\r\n          <div className=\"text-xs\">Active Forms: {Object.keys(forms).length}</div>\r\n          <div className=\"flex items-center gap-2\">\r\n            <span className=\"text-xs\">Example Form: {exampleFormState ? 'Active' : 'Inactive'}</span>\r\n            <button\r\n              onClick={handleFormExample}\r\n              className=\"px-2 py-1 rounded bg-teal-600 text-white hover:bg-teal-700 text-xs\"\r\n            >\r\n              Set Form\r\n            </button>\r\n            {exampleFormState && (\r\n              <button\r\n                onClick={resetExampleForm}\r\n                className=\"px-2 py-1 rounded bg-red-600 text-white hover:bg-red-700 text-xs\"\r\n              >\r\n                Reset\r\n              </button>\r\n            )}\r\n          </div>\r\n          {exampleFormState && (\r\n            <div className=\"text-xs text-gray-600\">\r\n              Valid: {exampleFormState.isValid ? 'Yes' : 'No'}, \r\n              Dirty: {exampleFormState.dirty ? 'Yes' : 'No'}\r\n            </div>\r\n          )}\r\n        </section>\r\n\r\n        {/* Loading States */}\r\n        <section className=\"space-y-2\">\r\n          <div className=\"font-medium text-indigo-700\">Loading States</div>\r\n          <div className=\"flex items-center gap-2\">\r\n            <span className=\"text-xs\">Example Action: {exampleLoading ? 'Loading...' : 'Idle'}</span>\r\n            <button\r\n              onClick={handleAsyncAction}\r\n              disabled={exampleLoading}\r\n              className=\"px-2 py-1 rounded bg-indigo-600 text-white hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed text-xs\"\r\n            >\r\n              {exampleLoading ? 'Loading...' : 'Start Action'}\r\n            </button>\r\n          </div>\r\n        </section>\r\n\r\n        {/* System Info */}\r\n        <section className=\"space-y-2\">\r\n          <div className=\"font-medium text-gray-700\">System</div>\r\n          <div className=\"text-xs\">\r\n            Online: {isOnline ? 'Yes' : 'No'} | Mobile: {isMobile ? 'Yes' : 'No'}\r\n          </div>\r\n        </section>\r\n\r\n        {/* State Debug */}\r\n        <section className=\"space-y-2\">\r\n          <div className=\"font-medium text-gray-700\">State (Zustand + Context)</div>\r\n          <pre className=\"bg-gray-100 p-2 rounded text-[10px] overflow-auto max-h-32\">\r\n            {JSON.stringify({\r\n              auth: state.auth,\r\n              ui: {\r\n                ...state.ui,\r\n                notifications: `${state.ui.notifications.length} items`\r\n              },\r\n              settings: state.settings,\r\n              navigation: state.navigation,\r\n              system: { isOnline: state.isOnline, isMobile: state.isMobile },\r\n              forms: Object.keys(state.forms)\r\n            }, null, 2)}\r\n          </pre>\r\n        </section>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\n// Example of how to wrap your app with the provider\r\nexport function AppWrapper({ children }: { children: React.ReactNode }) {\r\n  return (\r\n    <div className=\"min-h-screen\">\r\n      {children}\r\n    </div>\r\n  )\r\n}\r\n\r\n// Example usage in a component showing the power of the hybrid approach\r\nexport function ExampleComponent() {\r\n  const { addNotification } = useUI()\r\n  const { user } = useAuth()\r\n  const exampleLoading = useLoadingState('example-action')\r\n  const { isOpen: modalOpen, open: openModal, close: closeModal } = useModalState('example-modal')\r\n\r\n  const handleAsyncAction = async () => {\r\n    const store = useAppContext()\r\n    store.setLoadingState('example-action', true)\r\n    \r\n    try {\r\n      // Simulate API call\r\n      await new Promise(resolve => setTimeout(resolve, 2000))\r\n      \r\n      addNotification({\r\n        type: 'success',\r\n        title: 'Success!',\r\n        message: 'Action completed successfully with Zustand + Context',\r\n        duration: 5000,\r\n      })\r\n    } catch (error) {\r\n      addNotification({\r\n        type: 'error',\r\n        title: 'Error!',\r\n        message: 'Something went wrong',\r\n        duration: 5000,\r\n      })\r\n    } finally {\r\n      store.setLoadingState('example-action', false)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"p-4 space-y-4\">\r\n      <h2 className=\"text-xl font-bold\">Example Component</h2>\r\n      <p>Welcome, {user?.firstName || 'Guest'}!</p>\r\n      \r\n      <div className=\"space-x-2\">\r\n        <button \r\n          onClick={handleAsyncAction}\r\n          disabled={exampleLoading}\r\n          className=\"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50\"\r\n        >\r\n          {exampleLoading ? 'Loading...' : 'Perform Async Action'}\r\n        </button>\r\n        \r\n        <button \r\n          onClick={openModal}\r\n          className=\"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600\"\r\n        >\r\n          Open Modal\r\n        </button>\r\n      </div>\r\n\r\n      {modalOpen && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white p-6 rounded-lg\">\r\n            <h3 className=\"text-lg font-bold mb-4\">Example Modal</h3>\r\n            <p className=\"mb-4\">This modal state is managed by Zustand + Context!</p>\r\n            <button \r\n              onClick={closeModal}\r\n              className=\"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600\"\r\n            >\r\n              Close Modal\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;;;AAGA;AACA;AAJA;;;;AAOO,SAAS;IACd,uDAAuD;IACvD,MAAM,QAAQ,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;IAE1B,qDAAqD;IACrD,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD;IACzD,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,aAAa,EAAE,QAAQ,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,QAAK,AAAD;IAC5F,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,cAAW,AAAD;IAC/C,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;IACtD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,YAAS,AAAD;IAEvC,2CAA2C;IAC3C,MAAM,iBAAiB,CAAA,GAAA,yIAAA,CAAA,kBAAe,AAAD,EAAE;IACvC,MAAM,EAAE,QAAQ,gBAAgB,EAAE,MAAM,gBAAgB,EAAE,OAAO,iBAAiB,EAAE,QAAQ,kBAAkB,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD,EAAE;IACjI,MAAM,EAAE,WAAW,gBAAgB,EAAE,cAAc,mBAAmB,EAAE,WAAW,gBAAgB,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,eAAY,AAAD,EAAE;IAErH,MAAM,cAAc;QAClB,MAAM,WAAW;YACf,IAAI;YACJ,OAAO;YACP,WAAW;YACX,UAAU;YACV,MAAM;YACN,iBAAiB;YACjB,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,QAAQ;QACR,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;QACZ;IACF;IAEA,MAAM,eAAe;QACnB;QACA,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;QACZ;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,OAAO,UAAU,UAAU,SAAS,UAAU,SAAS,WAAW;QACxE,SAAS;QACT,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS,CAAC,YAAY,EAAE,KAAK,MAAM,CAAC;YACpC,UAAU;QACZ;IACF;IAEA,MAAM,oBAAoB;QACxB,oBAAoB;YAClB,OAAO;YACP,SAAS;YACT,QAAQ;gBAAE,OAAO;YAAuB;YACxC,SAAS;gBAAE,OAAO;YAAK;QACzB;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,eAAe,CAAC,kBAAkB;QAExC,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,UAAU;YACZ;QACF,SAAU;YACR,MAAM,eAAe,CAAC,kBAAkB;QAC1C;IACF;IAEA,gEAAgE;IAChE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAgB;;;;;;kCAC9B,8OAAC,+IAAA,CAAA,kBAAe;wBAAC,UAAU;wBAAU,UAAU;;;;;;;;;;;;0BAGjD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAI,WAAU;0CAA4B;;;;;;0CAC3C,8OAAC;;oCAAE;oCAAS,kBAAkB,cAAc;;;;;;;4BAC3C,sBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAK,KAAK,SAAS;4CAAC;4CAAE,KAAK,QAAQ;4CAAC;4CAAI,KAAK,KAAK;;;;;;;kDACnD,8OAAC;;4CAAI;4CAAO,KAAK,IAAI;;;;;;;;;;;;;0CAGzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,SAAS;wCAAa,WAAU;kDAAqE;;;;;;kDAC7G,8OAAC;wCAAO,SAAS;wCAAc,WAAU;kDAAmE;;;;;;;;;;;;;;;;;;kCAKhH,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAI,WAAU;0CAA6B;;;;;;0CAC5C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAAU;4CAAQ;;;;;;;kDAClC,8OAAC;wCAAO,SAAS;wCAAmB,WAAU;kDAAqE;;;;;;;;;;;;0CAErH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAAU;4CAAU,cAAc,SAAS;;;;;;;kDAC3D,8OAAC;wCAAO,SAAS;wCAAe,WAAU;kDAAuE;;;;;;;;;;;;0CAEnH,8OAAC;gCAAI,WAAU;;oCAAU;oCAAgB,cAAc,MAAM;;;;;;;0CAG7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAAU;4CAAgB,mBAAmB,SAAS;;;;;;;kDACtE,8OAAC;wCAAO,SAAS;wCAAoB,WAAU;kDAAyE;;;;;;;;;;;;;;;;;;kCAK5H,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAI,WAAU;0CAA8B;;;;;;0CAC7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAAU;4CAAsB,SAAS,kBAAkB,GAAG,OAAO;;;;;;;kDACrF,8OAAC;wCACC,SAAS,IAAM,eAAe;gDAAE,oBAAoB,CAAC,SAAS,kBAAkB;4CAAC;wCACjF,WAAU;kDACX;;;;;;;;;;;;0CAIH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAAU;4CAAY,SAAS,QAAQ,GAAG,OAAO;;;;;;;kDACjE,8OAAC;wCACC,SAAS,IAAM,eAAe;gDAAE,UAAU,CAAC,SAAS,QAAQ;4CAAC;wCAC7D,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAOL,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAI,WAAU;0CAA8B;;;;;;0CAC7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;wCAAmB,OAAO;;4CAAc;4CAAU;;;;;;;kDAClE,8OAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAOL,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAI,WAAU;0CAA4B;;;;;;0CAC3C,8OAAC;gCAAI,WAAU;;oCAAU;oCAAe,OAAO,IAAI,CAAC,OAAO,MAAM;;;;;;;0CACjE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAAU;4CAAe,mBAAmB,WAAW;;;;;;;kDACvE,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;oCAGA,kCACC,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;4BAKJ,kCACC,8OAAC;gCAAI,WAAU;;oCAAwB;oCAC7B,iBAAiB,OAAO,GAAG,QAAQ;oCAAK;oCACxC,iBAAiB,KAAK,GAAG,QAAQ;;;;;;;;;;;;;kCAM/C,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAI,WAAU;0CAA8B;;;;;;0CAC7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAAU;4CAAiB,iBAAiB,eAAe;;;;;;;kDAC3E,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,iBAAiB,eAAe;;;;;;;;;;;;;;;;;;kCAMvC,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAI,WAAU;0CAA4B;;;;;;0CAC3C,8OAAC;gCAAI,WAAU;;oCAAU;oCACd,WAAW,QAAQ;oCAAK;oCAAY,WAAW,QAAQ;;;;;;;;;;;;;kCAKpE,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAI,WAAU;0CAA4B;;;;;;0CAC3C,8OAAC;gCAAI,WAAU;0CACZ,KAAK,SAAS,CAAC;oCACd,MAAM,MAAM,IAAI;oCAChB,IAAI;wCACF,GAAG,MAAM,EAAE;wCACX,eAAe,GAAG,MAAM,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC;oCACzD;oCACA,UAAU,MAAM,QAAQ;oCACxB,YAAY,MAAM,UAAU;oCAC5B,QAAQ;wCAAE,UAAU,MAAM,QAAQ;wCAAE,UAAU,MAAM,QAAQ;oCAAC;oCAC7D,OAAO,OAAO,IAAI,CAAC,MAAM,KAAK;gCAChC,GAAG,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAMrB;AAGO,SAAS,WAAW,EAAE,QAAQ,EAAiC;IACpE,qBACE,8OAAC;QAAI,WAAU;kBACZ;;;;;;AAGP;AAGO,SAAS;IACd,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,QAAK,AAAD;IAChC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,iBAAiB,CAAA,GAAA,yIAAA,CAAA,kBAAe,AAAD,EAAE;IACvC,MAAM,EAAE,QAAQ,SAAS,EAAE,MAAM,SAAS,EAAE,OAAO,UAAU,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD,EAAE;IAEhF,MAAM,oBAAoB;QACxB,MAAM,QAAQ,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;QAC1B,MAAM,eAAe,CAAC,kBAAkB;QAExC,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,UAAU;YACZ;QACF,SAAU;YACR,MAAM,eAAe,CAAC,kBAAkB;QAC1C;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAoB;;;;;;0BAClC,8OAAC;;oBAAE;oBAAU,MAAM,aAAa;oBAAQ;;;;;;;0BAExC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAU;kCAET,iBAAiB,eAAe;;;;;;kCAGnC,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;YAKF,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyB;;;;;;sCACvC,8OAAC;4BAAE,WAAU;sCAAO;;;;;;sCACpB,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 2795, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/hooks/context/index.ts"], "sourcesContent": ["// Export all context-related hooks and types\r\nexport {\r\n  AppProvider,\r\n  useAppContext,\r\n  useAuth,\r\n  useUI,\r\n  useSettings,\r\n  useNavigation,\r\n  useForms,\r\n  useSystem,\r\n  useLoadingState,\r\n  useModalState,\r\n  useFormState,\r\n} from './useAppContext'\r\n\r\nexport {\r\n  createAppStore,\r\n  useAppStore,\r\n} from './useAppStore'\r\n\r\nexport {\r\n  ContextDemo,\r\n  AppWrapper,\r\n  ExampleComponent,\r\n} from './contextDemo'\r\n\r\n// Export types\r\nexport type {\r\n  AppState,\r\n  AuthState,\r\n  UIState,\r\n  SettingsState,\r\n  NavigationState,\r\n  FormState,\r\n  User,\r\n  Notification,\r\n  Breadcrumb,\r\n} from './useAppState'\r\n\r\nexport type {\r\n  AppStore,\r\n} from './useAppStore'\r\n\r\n// Export initial states\r\nexport {\r\n  initialAppState,\r\n  initialAuthState,\r\n  initialUIState,\r\n  initialSettingsState,\r\n  initialNavigationState,\r\n} from './useAppState' "], "names": [], "mappings": "AAAA,6CAA6C;;AAC7C;AAcA;AAKA;AAuBA,wBAAwB;AACxB", "debugId": null}}, {"offset": {"line": 2820, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn(\r\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn(\"aspect-square size-full\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn(\r\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2870, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/general/AppHeader.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON> } from \"lucide-react\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { Button } from \"@/components/ui/button\"\n\ninterface AppHeaderProps {\n  user?: {\n    name: string\n    email: string\n    avatar: string\n  }\n}\n\nexport function AppHeader({ user }: AppHeaderProps) {\n  const defaultUser = {\n    name: \"Adebay<PERSON>\",\n    email: \"<EMAIL>\",\n    avatar: \"https://i.pravatar.cc/150?img=1\"\n  }\n\n  const currentUser = user || defaultUser\n\n  return (\n    <header className=\"flex items-center justify-end px-6 py-4 bg-background border-b border-border\">\n      <div className=\"flex items-center gap-4\">\n        {/* Notification Bell */}\n        <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\n          <Bell size={20} className=\"text-health-muted\" />\n          {/* Optional notification dot */}\n          <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-health-error rounded-full\"></span>\n        </Button>\n\n        {/* User Info */}\n        <div className=\"flex items-center gap-3\">\n          <Avatar className=\"h-10 w-10\">\n            <AvatarImage src={currentUser.avatar} alt={currentUser.name} />\n            <AvatarFallback className=\"bg-health-light text-health-secondary font-medium\">\n              {currentUser.name.charAt(0).toUpperCase()}\n            </AvatarFallback>\n          </Avatar>\n          <div className=\"flex flex-col\">\n            <span className=\"health-body-md font-medium text-health-secondary\">\n              {currentUser.name}\n            </span>\n            <span className=\"health-body-sm text-health-muted\">\n              {currentUser.email}\n            </span>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAcO,SAAS,UAAU,EAAE,IAAI,EAAkB;IAChD,MAAM,cAAc;QAClB,MAAM;QACN,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,cAAc,QAAQ;IAE5B,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAO,WAAU;;sCAC5C,8OAAC,kMAAA,CAAA,OAAI;4BAAC,MAAM;4BAAI,WAAU;;;;;;sCAE1B,8OAAC;4BAAK,WAAU;;;;;;;;;;;;8BAIlB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BAAC,WAAU;;8CAChB,8OAAC,kIAAA,CAAA,cAAW;oCAAC,KAAK,YAAY,MAAM;oCAAE,KAAK,YAAY,IAAI;;;;;;8CAC3D,8OAAC,kIAAA,CAAA,iBAAc;oCAAC,WAAU;8CACvB,YAAY,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;sCAG3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;8CAEnB,8OAAC;oCAAK,WAAU;8CACb,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhC", "debugId": null}}, {"offset": {"line": 2995, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/app/layout.tsx"], "sourcesContent": ["\"use client\"\r\n\r\n// import type { Metadata } from \"next\";\r\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from \"next/font/google\";\r\nimport \"./globals.css\";\r\nimport { AppSidebar } from \"@/components/general/SideBar\";\nimport {\n  SidebarInset,\n  SidebarProvider,\n} from \"@/components/ui/sidebar\"\nimport { AppProvider, ContextDemo } from \"@/hooks/context\"\nimport { AppHeader } from \"@/components/general/AppHeader\"\n\r\nconst geistSans = Geist({\r\n  variable: \"--font-geist-sans\",\r\n  subsets: [\"latin\"],\r\n});\r\n\r\nconst geistMono = Geist_Mono({\r\n  variable: \"--font-geist-mono\",\r\n  subsets: [\"latin\"],\r\n});\r\n\r\n// export const metadata: Metadata = {\r\n//   title: \"Create Next App\",\r\n//   description: \"Generated by create next app\",\r\n// };\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  return (\r\n    <html lang=\"en\">\r\n      <body\r\n        className={`${geistSans.variable} ${geistMono.variable} antialiased`}\r\n      >\r\n        <AppProvider>\r\n          <SidebarProvider>\r\n            <AppSidebar />\r\n            <SidebarInset>\n              <AppHeader />\n              {children}\n            </SidebarInset>\n          </SidebarProvider>\r\n          {/* <ContextDemo /> */}\r\n        </AppProvider>\r\n      </body>\r\n    </html>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;;AAKA;AACA;AAIA;AAAA;AACA;AAXA;;;;;;;;;AA4Be,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YACC,WAAW,GAAG,yIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,8IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,YAAY,CAAC;sBAEpE,cAAA,8OAAC,yIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC,mIAAA,CAAA,kBAAe;;sCACd,8OAAC,wIAAA,CAAA,aAAU;;;;;sCACX,8OAAC,mIAAA,CAAA,eAAY;;8CACX,8OAAC,0IAAA,CAAA,YAAS;;;;;gCACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQf", "debugId": null}}]}