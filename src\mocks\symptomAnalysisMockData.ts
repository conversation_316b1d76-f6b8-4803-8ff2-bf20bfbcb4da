// Enums for symptom analysis page
export enum SeverityLevel {
  MILD = "Mild",
  MODERATE = "Moderate",
  SEVERE = "Severe"
}

export enum ConditionType {
  TENSION_HEADACHE = "Tension Headache",
  MIGRAINE = "Migraine",
  CLUSTER_HEADACHE = "Cluster Headache"
}

export enum TimeRange {
  LAST_7_DAYS = "Last 7 days",
  LAST_30_DAYS = "Last 30 days",
  LAST_3_MONTHS = "Last 3 months"
}

// String formatters
export const formatDate = (date: Date): string => {
  return date.toLocaleDateString('en-US', { 
    month: 'long', 
    day: 'numeric', 
    year: 'numeric' 
  });
};

export const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('en-US', { 
    hour: 'numeric', 
    minute: '2-digit', 
    hour12: true 
  });
};

export const formatPercentage = (value: number): string => {
  return `${value}%`;
};

export const formatBPM = (value: number): string => {
  return `${value} BPM`;
};

export const formatHours = (value: number): string => {
  return `${value} hours`;
};

// Data passed as props to the root component
export const mockRootProps = {
  symptomData: {
    type: "Headache" as const,
    severity: SeverityLevel.MILD,
    detectedDate: new Date("2025-05-24"),
    detectedTime: new Date("2025-05-24T10:30:00"),
    associatedFactors: ["Stress", "Lack of Sleep"] as const
  },
  aiInsights: {
    heartRate: 72,
    airQuality: "Moderate" as const,
    sleepHours: 6,
    analysisText: "The observed symptom patterns indicate potential early warning signs. It's important to analyze the trends closely and implement the suggested actions to address any issues."
  },
  potentialConditions: [
    {
      condition: ConditionType.TENSION_HEADACHE,
      probability: 70
    },
    {
      condition: ConditionType.MIGRAINE,
      probability: 25
    }
  ],
  healthAlerts: [
    "High pollen count today, may exacerbate allergies"
  ],
  recommendations: [
    "Monitor for 24 hours",
    "Consider over the counter pain relief", 
    "If symptoms worsen, consult a doctor"
  ],
  chartData: [
    { day: "Mon", value: 2, dayIndex: 0 },
    { day: "Tue", value: 3, dayIndex: 1 },
    { day: "Wed", value: 1, dayIndex: 2 },
    { day: "Thur", value: 4, dayIndex: 3 },
    { day: "Fri", value: 6, dayIndex: 4 },
    { day: "Sat", value: 3, dayIndex: 5 },
    { day: "Sun", value: 2, dayIndex: 6 }
  ],
  historicalData: [
    {
      dateLogged: new Date("2025-06-10"),
      severity: SeverityLevel.MODERATE,
      aiSummary: "Likely due to fatigue, monitor hydration"
    },
    {
      dateLogged: new Date("2025-06-10"),
      severity: SeverityLevel.MODERATE,
      aiSummary: "Likely due to fatigue, monitor hydration"
    },
    {
      dateLogged: new Date("2025-06-10"),
      severity: SeverityLevel.MODERATE,
      aiSummary: "Likely due to fatigue, monitor hydration"
    }
  ]
};

// Props types (data passed to components)
export interface SymptomData {
  type: string;
  severity: string;
  detectedDate: Date;
  detectedTime: Date;
  associatedFactors: string[];
}

export interface AIInsights {
  heartRate: number;
  airQuality: string;
  sleepHours: number;
  analysisText: string;
}

export interface PotentialCondition {
  condition: string;
  probability: number;
}

export interface ChartDataPoint {
  day: string;
  value: number;
  dayIndex: number;
}

export interface HistoricalEntry {
  dateLogged: Date;
  severity: string;
  aiSummary: string;
}

export interface SymptomAnalysisProps {
  symptomData: SymptomData;
  aiInsights: AIInsights;
  potentialConditions: PotentialCondition[];
  healthAlerts: string[];
  recommendations: string[];
  chartData: ChartDataPoint[];
  historicalData: HistoricalEntry[];
}