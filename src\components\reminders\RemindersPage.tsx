"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, Ta<PERSON>Content } from '@/components/ui/tabs';
import { BreadcrumbNavigation } from './BreadcrumbNavigation';
import { MedicationsTable } from './MedicationsTable';
import { AppointmentsTable } from './AppointmentsTable';
import { MedicationFrequency, AppointmentType } from '@/mocks/enums';

interface Medication {
  id: string;
  name: string;
  dosage: string;
  time: Date;
  frequency: MedicationFrequency;
}

interface Appointment {
  id: string;
  type: AppointmentType;
  dateTime: Date;
  provider: string;
}

interface RemindersPageProps {
  medications: Medication[];
  appointments: Appointment[];
}

export function RemindersPage({ medications, appointments }: RemindersPageProps) {
  const [activeTab, setActiveTab] = useState<"medications" | "appointments">("medications");

  const breadcrumbItems = [
    { label: "User Dashboard", href: "/dashboard" },
    { label: "Recent Symptom Analysis", href: "/symptom-analysis" },
    { label: "Upcoming Reminder" }
  ];

  const handleMedicationAction = (medicationId: string, action: string) => {
    console.log(`Medication action: ${action} for medication ${medicationId}`);
    // Implement action logic here
  };

  const handleAppointmentAction = (appointmentId: string, action: string) => {
    console.log(`Appointment action: ${action} for appointment ${appointmentId}`);
    // Implement action logic here
  };

  const handleAddReminder = () => {
    console.log('Add reminder clicked');
    // Implement add reminder logic here
  };

  return (
    <div className="min-h-screen bg-[#fff9f9] p-8">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Breadcrumb Navigation */}
        <BreadcrumbNavigation items={breadcrumbItems} />

        {/* Header with Add Reminder Button */}
        <div className="flex items-center justify-between">
          <h1 className="health-heading-lg text-[#191919]">AI- Powered Reminders</h1>
          <Button 
            className="health-button-primary"
            onClick={handleAddReminder}
          >
            Add Reminder
          </Button>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "medications" | "appointments")}>
          <TabsList className="grid w-fit grid-cols-2 gap-2">
            <TabsTrigger 
              value="medications" 
              className="health-body-lg data-[state=active]:text-[#394c6b] data-[state=inactive]:text-[#868686]"
            >
              Upcoming Medications
            </TabsTrigger>
            <TabsTrigger 
              value="appointments"
              className="health-body-lg data-[state=active]:text-[#394c6b] data-[state=inactive]:text-[#868686]"
            >
              Appointments
            </TabsTrigger>
          </TabsList>

          <TabsContent value="medications" className="mt-6">
            <MedicationsTable 
              medications={medications}
              onAction={handleMedicationAction}
            />
          </TabsContent>

          <TabsContent value="appointments" className="mt-6">
            <AppointmentsTable 
              appointments={appointments}
              onAction={handleAppointmentAction}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}