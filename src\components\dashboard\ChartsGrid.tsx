"use client";

import { MoodTrendChart } from "./charts/MoodTrendChart";
import { PainLevelLineChart } from "./charts/PainLevelLineChart";
import { PainLevelBarChart } from "./charts/PainLevelBarChart";
import { CircularProgressChart } from "./charts/CircularProgressChart";

interface ChartData {
  moodTrend: Array<{ day: string; value: number }>;
  painLevelLine: Array<{ day: string; value: number }>;
  painLevelBar: Array<{ day: string; value: number }>;
  painLevelCircular: { value: number; total: number };
}

interface ChartsGridProps {
  chartData: ChartData;
}

export function ChartsGrid({ chartData }: ChartsGridProps) {
  return (
    <section className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <MoodTrendChart data={chartData.moodTrend} />
      <PainLevelLineChart data={chartData.painLevelLine} />
      <PainLevelBarChart data={chartData.painLevelBar} />
      <CircularProgressChart data={chartData.painLevelCircular} />
    </section>
  );
}