"use client";

import { Card, CardContent } from "@/components/ui/card";

interface SymptomOverviewCardProps {
  title: string;
  value: string;
  variant?: "primary" | "secondary";
}

export function SymptomOverviewCard({ title, value, variant = "primary" }: SymptomOverviewCardProps) {
  return (
    <Card className="health-card-shadow health-rounded p-6">
      <CardContent className="p-0 space-y-2">
        <h3 className="health-body-lg">{title}</h3>
        <p className={variant === "primary" ? "health-body-sm" : "health-body-sm"}>
          {value}
        </p>
      </CardContent>
    </Card>
  );
}