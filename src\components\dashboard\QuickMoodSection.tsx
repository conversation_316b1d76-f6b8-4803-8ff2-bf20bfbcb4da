"use client";

import { Card, CardContent } from "@/components/ui/card";
import { MoodSelector } from "./MoodSelector";
import { SymptomLogger } from "./SymptomLogger";

export function QuickMoodSection() {
  const handleMoodChange = (mood: string | null) => {
    console.log("Mood selected:", mood);
  };

  const handleSymptomSubmit = (symptom: string) => {
    console.log("Symptom submitted:", symptom);
  };

  return (
    <section className="space-y-3 mb-6">
      <h2 className="text-4xl font-normal text-health-secondary mb-3">Quick Mood</h2>
      <Card className="shadow-lg rounded-2xl">
        <CardContent className="p-6 space-y-6">
          <MoodSelector onMoodChange={handleMoodChange} />
          <SymptomLogger onSubmit={handleSymptomSubmit} />
        </CardContent>
      </Card>
    </section>
  );
}