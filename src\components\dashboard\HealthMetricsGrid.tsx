"use client";

import { HealthMetricCard } from "./HealthMetricCard";

interface HealthMetric {
  id: string;
  title: string;
  icon: string;
  details: Record<string, any>;
  action: string;
  route?: string;
}

interface HealthMetricsGridProps {
  metrics: HealthMetric[];
}

export function HealthMetricsGrid({ metrics }: HealthMetricsGridProps) {
  return (
    <section className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-5 mb-6">
      {metrics.map((metric) => (
        <HealthMetricCard key={metric.id} {...metric} />
      ))}
    </section>
  );
}