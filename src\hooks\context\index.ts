// Export all context-related hooks and types
export {
  AppProvider,
  useAppContext,
  useAuth,
  useUI,
  useSettings,
  useNavigation,
  useForms,
  useSystem,
  useLoadingState,
  useModalState,
  useFormState,
} from './useAppContext'

export {
  createAppStore,
  useAppStore,
} from './useAppStore'

export {
  ContextDemo,
  AppWrapper,
  ExampleComponent,
} from './contextDemo'

// Export types
export type {
  AppState,
  AuthState,
  UIState,
  SettingsState,
  NavigationState,
  FormState,
  User,
  Notification,
  Breadcrumb,
} from './useAppState'

export type {
  AppStore,
} from './useAppStore'

// Export initial states
export {
  initialAppState,
  initialAuthState,
  initialUIState,
  initialSettingsState,
  initialNavigationState,
} from './useAppState' 