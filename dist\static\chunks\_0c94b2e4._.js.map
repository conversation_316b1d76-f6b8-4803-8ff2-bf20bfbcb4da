{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }"], "names": [], "mappings": ";;;;;;;AAEA;;;;;;AAGA;AALA;;;;;AAOA,MAAM,OAAO,cAAc,IAAI;AAE/B,MAAM,yBAAW,6JAAA,CAAA,aAAgB,MAG/B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,cAAc,IAAI;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;;;AAGb,SAAS,WAAW,GAAG,cAAc,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,cAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAED,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,cAAc,OAAO,CAAC,WAAW;AAE3D,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,cAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,cAAc,OAAO,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { ChevronRight, MoreHorizontal } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Breadcrumb({ ...props }: React.ComponentProps<\"nav\">) {\r\n  return <nav aria-label=\"breadcrumb\" data-slot=\"breadcrumb\" {...props} />\r\n}\r\n\r\nfunction BreadcrumbList({ className, ...props }: React.ComponentProps<\"ol\">) {\r\n  return (\r\n    <ol\r\n      data-slot=\"breadcrumb-list\"\r\n      className={cn(\r\n        \"text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-item\"\r\n      className={cn(\"inline-flex items-center gap-1.5\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbLink({\r\n  asChild,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"breadcrumb-link\"\r\n      className={cn(\"hover:text-foreground transition-colors\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbPage({ className, ...props }: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-page\"\r\n      role=\"link\"\r\n      aria-disabled=\"true\"\r\n      aria-current=\"page\"\r\n      className={cn(\"text-foreground font-normal\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbSeparator({\r\n  children,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-separator\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"[&>svg]:size-3.5\", className)}\r\n      {...props}\r\n    >\r\n      {children ?? <ChevronRight />}\r\n    </li>\r\n  )\r\n}\r\n\r\nfunction BreadcrumbEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-ellipsis\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"flex size-9 items-center justify-center\", className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontal className=\"size-4\" />\r\n      <span className=\"sr-only\">More</span>\r\n    </span>\r\n  )\r\n}\r\n\r\nexport {\r\n  Breadcrumb,\r\n  BreadcrumbList,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n  BreadcrumbEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AAAA;AAEA;;;;;AAEA,SAAS,WAAW,KAAyC;QAAzC,EAAE,GAAG,OAAoC,GAAzC;IAClB,qBAAO,6LAAC;QAAI,cAAW;QAAa,aAAU;QAAc,GAAG,KAAK;;;;;;AACtE;KAFS;AAIT,SAAS,eAAe,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACtB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACtB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,eAAe,KAMvB;QANuB,EACtB,OAAO,EACP,SAAS,EACT,GAAG,OAGJ,GANuB;IAOtB,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MAhBS;AAkBT,SAAS,eAAe,KAAqD;QAArD,EAAE,SAAS,EAAE,GAAG,OAAqC,GAArD;IACtB,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,oBAAoB,KAIA;QAJA,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB,GAJA;IAK3B,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER,qBAAA,sBAAA,yBAAY,6LAAC,yNAAA,CAAA,eAAY;;;;;;;;;;AAGhC;MAhBS;AAkBT,SAAS,mBAAmB,KAGG;QAHH,EAC1B,SAAS,EACT,GAAG,OAC0B,GAHH;IAI1B,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;0BAET,6LAAC,mNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;MAhBS", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/icons/ChevronRightIcon.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport type { SVGProps } from \"react\";\nconst Component = (props: SVGProps<SVGSVGElement>) => <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 7.006 13.341\" {...props}><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"currentColor\" d=\"M.188 1.125a.66.66 0 0 1-.124-.73.665.665 0 0 1 .881-.337.7.7 0 0 1 .213.15l5.666 6a.667.667 0 0 1 0 .917l-5.666 6a.67.67 0 0 1-.727.173.67.67 0 0 1-.385-.867.7.7 0 0 1 .142-.221l5.235-5.543z\" /></svg>;\nexport default Component;"], "names": [], "mappings": ";;;;;AAEA,MAAM,YAAY,CAAC,sBAAmC,6LAAC;QAAI,OAAM;QAA6B,MAAK;QAAO,SAAQ;QAAoB,GAAG,KAAK;kBAAE,cAAA,6LAAC;YAAK,OAAM;YAA6B,MAAK;YAAe,GAAE;;;;;;;;;;;KAAzM;uCACS", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/reminders/BreadcrumbNavigation.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ist,\n  BreadcrumbItem,\n  BreadcrumbLink,\n  BreadcrumbSeparator,\n  BreadcrumbPage,\n} from '@/components/ui/breadcrumb';\nimport ChevronRightIcon from '@/components/icons/ChevronRightIcon';\n\ninterface BreadcrumbItem {\n  label: string;\n  href?: string;\n}\n\ninterface BreadcrumbNavigationProps {\n  items: BreadcrumbItem[];\n}\n\nexport function BreadcrumbNavigation({ items }: BreadcrumbNavigationProps) {\n  return (\n    <Breadcrumb>\n      <BreadcrumbList className=\"health-caption\">\n        {items.map((item, index) => (\n          <React.Fragment key={index}>\n            <BreadcrumbItem>\n              {index === items.length - 1 ? (\n                <BreadcrumbPage className=\"health-caption\">{item.label}</BreadcrumbPage>\n              ) : (\n                <BreadcrumbLink href={item.href} className=\"health-caption\">\n                  {item.label}\n                </BreadcrumbLink>\n              )}\n            </BreadcrumbItem>\n            {index < items.length - 1 && (\n              <BreadcrumbSeparator>\n                <ChevronRightIcon width={7} height={13} color=\"#868686\" />\n              </BreadcrumbSeparator>\n            )}\n          </React.Fragment>\n        ))}\n      </BreadcrumbList>\n    </Breadcrumb>\n  );\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AAQA;;;;;AAWO,SAAS,qBAAqB,KAAoC;QAApC,EAAE,KAAK,EAA6B,GAApC;IACnC,qBACE,6LAAC,yIAAA,CAAA,aAAU;kBACT,cAAA,6LAAC,yIAAA,CAAA,iBAAc;YAAC,WAAU;sBACvB,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;sCACb,6LAAC,yIAAA,CAAA,iBAAc;sCACZ,UAAU,MAAM,MAAM,GAAG,kBACxB,6LAAC,yIAAA,CAAA,iBAAc;gCAAC,WAAU;0CAAkB,KAAK,KAAK;;;;;qDAEtD,6LAAC,yIAAA,CAAA,iBAAc;gCAAC,MAAM,KAAK,IAAI;gCAAE,WAAU;0CACxC,KAAK,KAAK;;;;;;;;;;;wBAIhB,QAAQ,MAAM,MAAM,GAAG,mBACtB,6LAAC,yIAAA,CAAA,sBAAmB;sCAClB,cAAA,6LAAC,kJAAA,CAAA,UAAgB;gCAAC,OAAO;gCAAG,QAAQ;gCAAI,OAAM;;;;;;;;;;;;mBAZ/B;;;;;;;;;;;;;;;AAoB/B;KAzBgB", "debugId": null}}, {"offset": {"line": 364, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACb,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,KAGY;QAHZ,EACpB,SAAS,EACT,GAAG,OAC6B,GAHZ;IAIpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 510, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/icons/CheckboxIcon.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport type { SVGProps } from \"react\";\nconst Component = (props: SVGProps<SVGSVGElement>) => <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 16 16\" {...props}><g xmlns=\"http://www.w3.org/2000/svg\" stroke=\"#394C6B\" strokeLinejoin=\"round\"><path strokeLinecap=\"round\" d=\"m11 5.5-4.2 5-1.8-2\" /><path d=\"M12.5 2h-9A1.5 1.5 0 0 0 2 3.5v9c0 .828.67 1.5 1.5 1.5h9a1.5 1.5 0 0 0 1.5-1.5v-9A1.5 1.5 0 0 0 12.5 2z\" /></g></svg>;\nexport default Component;"], "names": [], "mappings": ";;;;;AAEA,MAAM,YAAY,CAAC,sBAAmC,6LAAC;QAAI,OAAM;QAA6B,MAAK;QAAO,SAAQ;QAAa,GAAG,KAAK;kBAAE,cAAA,6LAAC;YAAE,OAAM;YAA6B,QAAO;YAAU,gBAAe;;8BAAQ,6LAAC;oBAAK,eAAc;oBAAQ,GAAE;;;;;;8BAAwB,6LAAC;oBAAK,GAAE;;;;;;;;;;;;;;;;;KAA/Q;uCACS", "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/icons/ViewIcon.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport type { SVGProps } from \"react\";\nconst Component = (props: SVGProps<SVGSVGElement>) => <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 12.764 11.715\" {...props}><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"currentColor\" d=\"M4.922 8.123h2.92v-.59h-2.12l2.12-2.382v-.618h-2.92v.59h2.15l-2.15 2.415zm1.46 3.592c-.74 0-1.43-.139-2.08-.417a5.4 5.4 0 0 1-1.69-1.143 5.3 5.3 0 0 1-1.15-1.695 5.3 5.3 0 0 1-.41-2.078c0-.74.14-1.433.41-2.079a5.394 5.394 0 0 1 2.84-2.837 5.2 5.2 0 0 1 2.08-.418c.74 0 1.43.14 2.08.418a5.4 5.4 0 0 1 2.84 2.837c.27.646.41 1.34.41 2.08 0 .738-.14 1.431-.41 2.077a5.3 5.3 0 0 1-1.15 1.696c-.48.484-1.05.864-1.69 1.142-.65.277-1.34.416-2.08.417M2.322 0l.47.471-2.32 2.321-.47-.472zm8.12 0 2.32 2.32-.47.472-2.32-2.32zm-4.06 11.048q1.935 0 3.3-1.364c.91-.91 1.37-2.01 1.37-3.302s-.46-2.393-1.37-3.302q-1.365-1.365-3.3-1.365t-3.3 1.365c-.91.91-1.37 2.01-1.37 3.302 0 1.291.46 2.392 1.37 3.302q1.365 1.365 3.3 1.364\" /></svg>;\nexport default Component;"], "names": [], "mappings": ";;;;;AAEA,MAAM,YAAY,CAAC,sBAAmC,6LAAC;QAAI,OAAM;QAA6B,MAAK;QAAO,SAAQ;QAAqB,GAAG,KAAK;kBAAE,cAAA,6LAAC;YAAK,OAAM;YAA6B,MAAK;YAAe,GAAE;;;;;;;;;;;KAA1M;uCACS", "debugId": null}}, {"offset": {"line": 601, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/icons/EditIcon.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport type { SVGProps } from \"react\";\nconst Component = (props: SVGProps<SVGSVGElement>) => <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 12.333 11.781\" {...props}><path xmlns=\"http://www.w3.org/2000/svg\" stroke=\"#394C6B\" strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m7.833 1.948 2 2m-3.33 7.333h5.33M1.163 8.615l-.66 2.666 2.66-.666 7.73-7.724a1.333 1.333 0 0 0 0-1.886l-.12-.114c-.25-.25-.59-.391-.94-.391s-.69.14-.94.39z\" /></svg>;\nexport default Component;"], "names": [], "mappings": ";;;;;AAEA,MAAM,YAAY,CAAC,sBAAmC,6LAAC;QAAI,OAAM;QAA6B,MAAK;QAAO,SAAQ;QAAqB,GAAG,KAAK;kBAAE,cAAA,6LAAC;YAAK,OAAM;YAA6B,QAAO;YAAU,eAAc;YAAQ,gBAAe;YAAQ,GAAE;;;;;;;;;;;KAApP;uCACS", "debugId": null}}, {"offset": {"line": 640, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/icons/DeleteIcon.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport type { SVGProps } from \"react\";\nconst Component = (props: SVGProps<SVGSVGElement>) => <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 10.333 10.333\" {...props}><path xmlns=\"http://www.w3.org/2000/svg\" stroke=\"#394C6B\" strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m.497 9.834 4.67-4.667m0 0L9.837.5m-4.67 4.667L.497.5m4.67 4.667 4.67 4.667\" /></svg>;\nexport default Component;"], "names": [], "mappings": ";;;;;AAEA,MAAM,YAAY,CAAC,sBAAmC,6LAAC;QAAI,OAAM;QAA6B,MAAK;QAAO,SAAQ;QAAqB,GAAG,KAAK;kBAAE,cAAA,6LAAC;YAAK,OAAM;YAA6B,QAAO;YAAU,eAAc;YAAQ,gBAAe;YAAQ,GAAE;;;;;;;;;;;KAApP;uCACS", "debugId": null}}, {"offset": {"line": 679, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/icons/EyeIcon.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport type { SVGProps } from \"react\";\nconst Component = (props: SVGProps<SVGSVGElement>) => <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 11.693 8\" {...props}><g xmlns=\"http://www.w3.org/2000/svg\" stroke=\"#394C6B\"><path d=\"M5.847 6.333A2.33 2.33 0 0 0 8.177 4a2.33 2.33 0 1 0-4.66 0 2.33 2.33 0 0 0 2.33 2.333z\" /><path d=\"M11.307 3.289c.26.315.39.472.39.711s-.13.396-.39.711C10.357 5.86 8.267 8 5.847 8S1.337 5.86.387 4.711c-.26-.315-.39-.472-.39-.711s.13-.396.39-.711C1.337 2.14 3.427 0 5.847 0s4.51 2.14 5.46 3.289z\" /></g></svg>;\nexport default Component;"], "names": [], "mappings": ";;;;;AAEA,MAAM,YAAY,CAAC,sBAAmC,6LAAC;QAAI,OAAM;QAA6B,MAAK;QAAO,SAAQ;QAAgB,GAAG,KAAK;kBAAE,cAAA,6LAAC;YAAE,OAAM;YAA6B,QAAO;;8BAAU,6LAAC;oBAAK,GAAE;;;;;;8BAA4F,6LAAC;oBAAK,GAAE;;;;;;;;;;;;;;;;;KAAzS;uCACS", "debugId": null}}, {"offset": {"line": 731, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/icons/RefreshIcon.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport type { SVGProps } from \"react\";\nconst Component = (props: SVGProps<SVGSVGElement>) => <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 16 16\" {...props}><path xmlns=\"http://www.w3.org/2000/svg\" fill=\"currentColor\" d=\"M15.44 9.6q.105 0 .21.043c.07.029.13.07.19.123q.075.08.12.184a.5.5 0 0 1 .04.217v2.563c0 .074-.01.148-.04.216a.6.6 0 0 1-.12.184.6.6 0 0 1-.18.123.6.6 0 0 1-.22.045.6.6 0 0 1-.22-.045.6.6 0 0 1-.18-.123.6.6 0 0 1-.12-.184.5.5 0 0 1-.04-.216v-.9C13.42 14.275 10.72 16 7.81 16c-3.53 0-6.49-2.177-7.77-5.532a.54.54 0 0 1 .01-.433.553.553 0 0 1 1.03.023c1.12 2.93 3.67 4.808 6.73 4.808 2.69 0 5.21-1.745 6.39-4.123l-1.18.008a.6.6 0 0 1-.22-.042.5.5 0 0 1-.18-.122.64.64 0 0 1-.17-.399c0-.149.06-.294.16-.401a.58.58 0 0 1 .4-.17zM8.2 0c3.52 0 6.48 2.177 7.76 5.532.06.14.05.295-.01.433a.57.57 0 0 1-.31.301.6.6 0 0 1-.21.038.58.58 0 0 1-.4-.176.5.5 0 0 1-.11-.186c-1.12-2.93-3.67-4.808-6.72-4.808-2.7 0-5.22 1.745-6.39 4.123l1.17-.008c.08 0 .15.014.22.042q.105.044.18.122c.05.053.1.115.12.183.03.069.05.142.05.216 0 .149-.06.294-.16.401a.58.58 0 0 1-.4.17L.56 6.4a.6.6 0 0 1-.21-.043.7.7 0 0 1-.19-.123.6.6 0 0 1-.12-.184.5.5 0 0 1-.04-.217V3.27c0-.314.25-.568.56-.568s.56.254.56.568v.9C2.58 1.725 5.28 0 8.19 0\" /></svg>;\nexport default Component;"], "names": [], "mappings": ";;;;;AAEA,MAAM,YAAY,CAAC,sBAAmC,6LAAC;QAAI,OAAM;QAA6B,MAAK;QAAO,SAAQ;QAAa,GAAG,KAAK;kBAAE,cAAA,6LAAC;YAAK,OAAM;YAA6B,MAAK;YAAe,GAAE;;;;;;;;;;;KAAlM;uCACS", "debugId": null}}, {"offset": {"line": 768, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/reminders/ActionButtons.tsx"], "sourcesContent": ["import React from 'react';\nimport { Button } from '@/components/ui/button';\nimport CheckboxIcon from '@/components/icons/CheckboxIcon';\nimport ViewIcon from '@/components/icons/ViewIcon';\nimport EditIcon from '@/components/icons/EditIcon';\nimport DeleteIcon from '@/components/icons/DeleteIcon';\nimport EyeIcon from '@/components/icons/EyeIcon';\nimport RefreshIcon from '@/components/icons/RefreshIcon';\n\ninterface ActionButtonsProps {\n  itemId: string;\n  type: 'medication' | 'appointment';\n  onAction: (id: string, action: string) => void;\n}\n\nexport function ActionButtons({ itemId, type, onAction }: ActionButtonsProps) {\n  if (type === 'medication') {\n    return (\n      <div className=\"flex items-center gap-2\">\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"h-4 w-4 p-0\"\n          onClick={() => onAction(itemId, 'complete')}\n        >\n          <CheckboxIcon width={16} height={16} color=\"#394c6b\" />\n        </Button>\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"h-4 w-4 p-0\"\n          onClick={() => onAction(itemId, 'view')}\n        >\n          <ViewIcon width={13} height={12} color=\"#394c6b\" />\n        </Button>\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"h-4 w-4 p-0\"\n          onClick={() => onAction(itemId, 'edit')}\n        >\n          <EditIcon width={12} height={12} color=\"#394c6b\" />\n        </Button>\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"h-4 w-4 p-0\"\n          onClick={() => onAction(itemId, 'delete')}\n        >\n          <DeleteIcon width={10} height={10} color=\"#394c6b\" />\n        </Button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex items-center gap-2\">\n      <Button\n        variant=\"ghost\"\n        size=\"icon\"\n        className=\"h-4 w-4 p-0\"\n        onClick={() => onAction(itemId, 'view')}\n      >\n        <EyeIcon width={12} height={8} color=\"#394c6b\" />\n      </Button>\n      <Button\n        variant=\"ghost\"\n        size=\"icon\"\n        className=\"h-4 w-4 p-0\"\n        onClick={() => onAction(itemId, 'refresh')}\n      >\n        <RefreshIcon width={16} height={16} color=\"#394c6b\" />\n      </Button>\n      <Button\n        variant=\"ghost\"\n        size=\"icon\"\n        className=\"h-4 w-4 p-0\"\n        onClick={() => onAction(itemId, 'delete')}\n      >\n        <DeleteIcon width={10} height={10} color=\"#394c6b\" />\n      </Button>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAQO,SAAS,cAAc,KAA8C;QAA9C,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAsB,GAA9C;IAC5B,IAAI,SAAS,cAAc;QACzB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS,IAAM,SAAS,QAAQ;8BAEhC,cAAA,6LAAC,8IAAA,CAAA,UAAY;wBAAC,OAAO;wBAAI,QAAQ;wBAAI,OAAM;;;;;;;;;;;8BAE7C,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS,IAAM,SAAS,QAAQ;8BAEhC,cAAA,6LAAC,0IAAA,CAAA,UAAQ;wBAAC,OAAO;wBAAI,QAAQ;wBAAI,OAAM;;;;;;;;;;;8BAEzC,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS,IAAM,SAAS,QAAQ;8BAEhC,cAAA,6LAAC,0IAAA,CAAA,UAAQ;wBAAC,OAAO;wBAAI,QAAQ;wBAAI,OAAM;;;;;;;;;;;8BAEzC,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS,IAAM,SAAS,QAAQ;8BAEhC,cAAA,6LAAC,4IAAA,CAAA,UAAU;wBAAC,OAAO;wBAAI,QAAQ;wBAAI,OAAM;;;;;;;;;;;;;;;;;IAIjD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,SAAS,QAAQ;0BAEhC,cAAA,6LAAC,yIAAA,CAAA,UAAO;oBAAC,OAAO;oBAAI,QAAQ;oBAAG,OAAM;;;;;;;;;;;0BAEvC,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,SAAS,QAAQ;0BAEhC,cAAA,6LAAC,6IAAA,CAAA,UAAW;oBAAC,OAAO;oBAAI,QAAQ;oBAAI,OAAM;;;;;;;;;;;0BAE5C,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,SAAS,QAAQ;0BAEhC,cAAA,6LAAC,4IAAA,CAAA,UAAU;oBAAC,OAAO;oBAAI,QAAQ;oBAAI,OAAM;;;;;;;;;;;;;;;;;AAIjD;KApEgB", "debugId": null}}, {"offset": {"line": 955, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/lib/formatters.ts"], "sourcesContent": ["// String formatters for reminders and appointments\nimport { MedicationFrequency, AppointmentType } from '../mocks/enums';\n\nexport const formatTime = (time: string): string => {\n  return time;\n};\n\nexport const formatDate = (date: Date): string => {\n  const today = new Date();\n  const tomorrow = new Date(today);\n  tomorrow.setDate(today.getDate() + 1);\n  \n  if (date.toDateString() === today.toDateString()) {\n    return `Today, ${date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true })}`;\n  } else if (date.toDateString() === tomorrow.toDateString()) {\n    return `Tomorrow, ${date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true })}`;\n  } else {\n    const dayName = date.toLocaleDateString('en-US', { weekday: 'long' });\n    const time = date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });\n    return `${dayName}, ${time}`;\n  }\n};\n\nexport const formatFrequency = (frequency: MedicationFrequency): string => {\n  return frequency;\n};\n\nexport const formatAppointmentType = (type: AppointmentType): string => {\n  return type;\n};"], "names": [], "mappings": "AAAA,mDAAmD;;;;;;;AAG5C,MAAM,aAAa,CAAC;IACzB,OAAO;AACT;AAEO,MAAM,aAAa,CAAC;IACzB,MAAM,QAAQ,IAAI;IAClB,MAAM,WAAW,IAAI,KAAK;IAC1B,SAAS,OAAO,CAAC,MAAM,OAAO,KAAK;IAEnC,IAAI,KAAK,YAAY,OAAO,MAAM,YAAY,IAAI;QAChD,OAAO,AAAC,UAAgG,OAAvF,KAAK,kBAAkB,CAAC,SAAS;YAAE,MAAM;YAAW,QAAQ;YAAW,QAAQ;QAAK;IACvG,OAAO,IAAI,KAAK,YAAY,OAAO,SAAS,YAAY,IAAI;QAC1D,OAAO,AAAC,aAAmG,OAAvF,KAAK,kBAAkB,CAAC,SAAS;YAAE,MAAM;YAAW,QAAQ;YAAW,QAAQ;QAAK;IAC1G,OAAO;QACL,MAAM,UAAU,KAAK,kBAAkB,CAAC,SAAS;YAAE,SAAS;QAAO;QACnE,MAAM,OAAO,KAAK,kBAAkB,CAAC,SAAS;YAAE,MAAM;YAAW,QAAQ;YAAW,QAAQ;QAAK;QACjG,OAAO,AAAC,GAAc,OAAZ,SAAQ,MAAS,OAAL;IACxB;AACF;AAEO,MAAM,kBAAkB,CAAC;IAC9B,OAAO;AACT;AAEO,MAAM,wBAAwB,CAAC;IACpC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1008, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/reminders/MedicationsTable.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableRow,\n  TableHead,\n  TableCell,\n} from '@/components/ui/table';\nimport { ActionButtons } from './ActionButtons';\nimport { formatDate, formatFrequency } from '@/lib/formatters';\nimport { MedicationFrequency } from '@/mocks/enums';\n\ninterface Medication {\n  id: string;\n  name: string;\n  dosage: string;\n  time: Date;\n  frequency: MedicationFrequency;\n}\n\ninterface MedicationsTableProps {\n  medications: Medication[];\n  onAction: (medicationId: string, action: string) => void;\n}\n\nexport function MedicationsTable({ medications, onAction }: MedicationsTableProps) {\n  return (\n    <div className=\"health-table-container\">\n      <Table>\n        <TableHeader>\n          <TableRow className=\"health-table-header\">\n            <TableHead className=\"health-body-lg text-[#191919] font-normal px-10\">Medication Name</TableHead>\n            <TableHead className=\"health-body-lg text-[#191919] font-normal\">Dosage</TableHead>\n            <TableHead className=\"health-body-lg text-[#191919] font-normal\">Time</TableHead>\n            <TableHead className=\"health-body-lg text-[#191919] font-normal\">Frequency</TableHead>\n            <TableHead className=\"health-body-lg text-[#191919] font-normal\">Action</TableHead>\n          </TableRow>\n        </TableHeader>\n        <TableBody>\n          {medications.map((medication) => (\n            <TableRow key={medication.id} className=\"health-table-row\">\n              <TableCell className=\"health-button-text px-10\">{medication.name}</TableCell>\n              <TableCell className=\"health-button-text\">{medication.dosage}</TableCell>\n              <TableCell className=\"health-button-text\">\n                {medication.time.toLocaleTimeString('en-US', { \n                  hour: 'numeric', \n                  minute: '2-digit', \n                  hour12: true \n                })}\n              </TableCell>\n              <TableCell className=\"health-button-text\">\n                {formatFrequency(medication.frequency)}\n              </TableCell>\n              <TableCell>\n                <ActionButtons\n                  itemId={medication.id}\n                  type=\"medication\"\n                  onAction={onAction}\n                />\n              </TableCell>\n            </TableRow>\n          ))}\n        </TableBody>\n      </Table>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AACA;AAQA;AACA;;;;;AAgBO,SAAS,iBAAiB,KAAgD;QAAhD,EAAE,WAAW,EAAE,QAAQ,EAAyB,GAAhD;IAC/B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;8BACJ,6LAAC,oIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,6LAAC,oIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAkD;;;;;;0CACvE,6LAAC,oIAAA,CAAA,YAAS;gCAAC,WAAU;0CAA4C;;;;;;0CACjE,6LAAC,oIAAA,CAAA,YAAS;gCAAC,WAAU;0CAA4C;;;;;;0CACjE,6LAAC,oIAAA,CAAA,YAAS;gCAAC,WAAU;0CAA4C;;;;;;0CACjE,6LAAC,oIAAA,CAAA,YAAS;gCAAC,WAAU;0CAA4C;;;;;;;;;;;;;;;;;8BAGrE,6LAAC,oIAAA,CAAA,YAAS;8BACP,YAAY,GAAG,CAAC,CAAC,2BAChB,6LAAC,oIAAA,CAAA,WAAQ;4BAAqB,WAAU;;8CACtC,6LAAC,oIAAA,CAAA,YAAS;oCAAC,WAAU;8CAA4B,WAAW,IAAI;;;;;;8CAChE,6LAAC,oIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB,WAAW,MAAM;;;;;;8CAC5D,6LAAC,oIAAA,CAAA,YAAS;oCAAC,WAAU;8CAClB,WAAW,IAAI,CAAC,kBAAkB,CAAC,SAAS;wCAC3C,MAAM;wCACN,QAAQ;wCACR,QAAQ;oCACV;;;;;;8CAEF,6LAAC,oIAAA,CAAA,YAAS;oCAAC,WAAU;8CAClB,CAAA,GAAA,2HAAA,CAAA,kBAAe,AAAD,EAAE,WAAW,SAAS;;;;;;8CAEvC,6LAAC,oIAAA,CAAA,YAAS;8CACR,cAAA,6LAAC,mJAAA,CAAA,gBAAa;wCACZ,QAAQ,WAAW,EAAE;wCACrB,MAAK;wCACL,UAAU;;;;;;;;;;;;2BAjBD,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;AA0BxC;KAzCgB", "debugId": null}}, {"offset": {"line": 1170, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/reminders/AppointmentsTable.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableRow,\n  TableHead,\n  TableCell,\n} from '@/components/ui/table';\nimport { ActionButtons } from './ActionButtons';\nimport { formatDate, formatAppointmentType } from '@/lib/formatters';\nimport { AppointmentType } from '@/mocks/enums';\n\ninterface Appointment {\n  id: string;\n  type: AppointmentType;\n  dateTime: Date;\n  provider: string;\n}\n\ninterface AppointmentsTableProps {\n  appointments: Appointment[];\n  onAction: (appointmentId: string, action: string) => void;\n}\n\nexport function AppointmentsTable({ appointments, onAction }: AppointmentsTableProps) {\n  return (\n    <div className=\"health-table-container\">\n      <Table>\n        <TableHeader>\n          <TableRow className=\"health-table-header\">\n            <TableHead className=\"health-body-lg text-[#191919] font-normal px-10\">Appointment Type</TableHead>\n            <TableHead className=\"health-body-lg text-[#191919] font-normal\">Date & Time</TableHead>\n            <TableHead className=\"health-body-lg text-[#191919] font-normal\">Provider</TableHead>\n            <TableHead className=\"health-body-lg text-[#191919] font-normal\">Action</TableHead>\n          </TableRow>\n        </TableHeader>\n        <TableBody>\n          {appointments.map((appointment) => (\n            <TableRow key={appointment.id} className=\"health-table-row\">\n              <TableCell className=\"health-button-text px-10\">\n                {formatAppointmentType(appointment.type)}\n              </TableCell>\n              <TableCell className=\"health-button-text\">\n                {formatDate(appointment.dateTime)}\n              </TableCell>\n              <TableCell className=\"health-button-text\">{appointment.provider}</TableCell>\n              <TableCell>\n                <ActionButtons\n                  itemId={appointment.id}\n                  type=\"appointment\"\n                  onAction={onAction}\n                />\n              </TableCell>\n            </TableRow>\n          ))}\n        </TableBody>\n      </Table>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AACA;AAQA;AACA;;;;;AAeO,SAAS,kBAAkB,KAAkD;QAAlD,EAAE,YAAY,EAAE,QAAQ,EAA0B,GAAlD;IAChC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;8BACJ,6LAAC,oIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,6LAAC,oIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAkD;;;;;;0CACvE,6LAAC,oIAAA,CAAA,YAAS;gCAAC,WAAU;0CAA4C;;;;;;0CACjE,6LAAC,oIAAA,CAAA,YAAS;gCAAC,WAAU;0CAA4C;;;;;;0CACjE,6LAAC,oIAAA,CAAA,YAAS;gCAAC,WAAU;0CAA4C;;;;;;;;;;;;;;;;;8BAGrE,6LAAC,oIAAA,CAAA,YAAS;8BACP,aAAa,GAAG,CAAC,CAAC,4BACjB,6LAAC,oIAAA,CAAA,WAAQ;4BAAsB,WAAU;;8CACvC,6LAAC,oIAAA,CAAA,YAAS;oCAAC,WAAU;8CAClB,CAAA,GAAA,2HAAA,CAAA,wBAAqB,AAAD,EAAE,YAAY,IAAI;;;;;;8CAEzC,6LAAC,oIAAA,CAAA,YAAS;oCAAC,WAAU;8CAClB,CAAA,GAAA,2HAAA,CAAA,aAAU,AAAD,EAAE,YAAY,QAAQ;;;;;;8CAElC,6LAAC,oIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB,YAAY,QAAQ;;;;;;8CAC/D,6LAAC,oIAAA,CAAA,YAAS;8CACR,cAAA,6LAAC,mJAAA,CAAA,gBAAa;wCACZ,QAAQ,YAAY,EAAE;wCACtB,MAAK;wCACL,UAAU;;;;;;;;;;;;2BAZD,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;AAqBzC;KAnCgB", "debugId": null}}, {"offset": {"line": 1312, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/components/reminders/RemindersPage.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, Ta<PERSON>Content } from '@/components/ui/tabs';\nimport { BreadcrumbNavigation } from './BreadcrumbNavigation';\nimport { MedicationsTable } from './MedicationsTable';\nimport { AppointmentsTable } from './AppointmentsTable';\nimport { MedicationFrequency, AppointmentType } from '@/mocks/enums';\n\ninterface Medication {\n  id: string;\n  name: string;\n  dosage: string;\n  time: Date;\n  frequency: MedicationFrequency;\n}\n\ninterface Appointment {\n  id: string;\n  type: AppointmentType;\n  dateTime: Date;\n  provider: string;\n}\n\ninterface RemindersPageProps {\n  medications: Medication[];\n  appointments: Appointment[];\n}\n\nexport function RemindersPage({ medications, appointments }: RemindersPageProps) {\n  const [activeTab, setActiveTab] = useState<\"medications\" | \"appointments\">(\"medications\");\n\n  const breadcrumbItems = [\n    { label: \"User Dashboard\", href: \"/dashboard\" },\n    { label: \"Recent Symptom Analysis\", href: \"/symptom-analysis\" },\n    { label: \"Upcoming Reminder\" }\n  ];\n\n  const handleMedicationAction = (medicationId: string, action: string) => {\n    console.log(`Medication action: ${action} for medication ${medicationId}`);\n    // Implement action logic here\n  };\n\n  const handleAppointmentAction = (appointmentId: string, action: string) => {\n    console.log(`Appointment action: ${action} for appointment ${appointmentId}`);\n    // Implement action logic here\n  };\n\n  const handleAddReminder = () => {\n    console.log('Add reminder clicked');\n    // Implement add reminder logic here\n  };\n\n  return (\n    <div className=\"min-h-screen bg-[#fff9f9] p-8\">\n      <div className=\"max-w-7xl mx-auto space-y-6\">\n        {/* Breadcrumb Navigation */}\n        <BreadcrumbNavigation items={breadcrumbItems} />\n\n        {/* Header with Add Reminder Button */}\n        <div className=\"flex items-center justify-between\">\n          <h1 className=\"health-heading-lg text-[#191919]\">AI- Powered Reminders</h1>\n          <Button \n            className=\"health-button-primary\"\n            onClick={handleAddReminder}\n          >\n            Add Reminder\n          </Button>\n        </div>\n\n        {/* Tabs */}\n        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as \"medications\" | \"appointments\")}>\n          <TabsList className=\"grid w-fit grid-cols-2 gap-2\">\n            <TabsTrigger \n              value=\"medications\" \n              className=\"health-body-lg data-[state=active]:text-[#394c6b] data-[state=inactive]:text-[#868686]\"\n            >\n              Upcoming Medications\n            </TabsTrigger>\n            <TabsTrigger \n              value=\"appointments\"\n              className=\"health-body-lg data-[state=active]:text-[#394c6b] data-[state=inactive]:text-[#868686]\"\n            >\n              Appointments\n            </TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"medications\" className=\"mt-6\">\n            <MedicationsTable \n              medications={medications}\n              onAction={handleMedicationAction}\n            />\n          </TabsContent>\n\n          <TabsContent value=\"appointments\" className=\"mt-6\">\n            <AppointmentsTable \n              appointments={appointments}\n              onAction={handleAppointmentAction}\n            />\n          </TabsContent>\n        </Tabs>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AA8BO,SAAS,cAAc,KAAiD;QAAjD,EAAE,WAAW,EAAE,YAAY,EAAsB,GAAjD;;IAC5B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkC;IAE3E,MAAM,kBAAkB;QACtB;YAAE,OAAO;YAAkB,MAAM;QAAa;QAC9C;YAAE,OAAO;YAA2B,MAAM;QAAoB;QAC9D;YAAE,OAAO;QAAoB;KAC9B;IAED,MAAM,yBAAyB,CAAC,cAAsB;QACpD,QAAQ,GAAG,CAAC,AAAC,sBAA8C,OAAzB,QAAO,oBAA+B,OAAb;IAC3D,8BAA8B;IAChC;IAEA,MAAM,0BAA0B,CAAC,eAAuB;QACtD,QAAQ,GAAG,CAAC,AAAC,uBAAgD,OAA1B,QAAO,qBAAiC,OAAd;IAC7D,8BAA8B;IAChC;IAEA,MAAM,oBAAoB;QACxB,QAAQ,GAAG,CAAC;IACZ,oCAAoC;IACtC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,0JAAA,CAAA,uBAAoB;oBAAC,OAAO;;;;;;8BAG7B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6LAAC,qIAAA,CAAA,SAAM;4BACL,WAAU;4BACV,SAAS;sCACV;;;;;;;;;;;;8BAMH,6LAAC,mIAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe,CAAC,QAAU,aAAa;;sCAC7D,6LAAC,mIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,6LAAC,mIAAA,CAAA,cAAW;oCACV,OAAM;oCACN,WAAU;8CACX;;;;;;8CAGD,6LAAC,mIAAA,CAAA,cAAW;oCACV,OAAM;oCACN,WAAU;8CACX;;;;;;;;;;;;sCAKH,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAc,WAAU;sCACzC,cAAA,6LAAC,sJAAA,CAAA,mBAAgB;gCACf,aAAa;gCACb,UAAU;;;;;;;;;;;sCAId,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAe,WAAU;sCAC1C,cAAA,6LAAC,uJAAA,CAAA,oBAAiB;gCAChB,cAAc;gCACd,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxB;GA3EgB;KAAA", "debugId": null}}, {"offset": {"line": 1492, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/mocks/enums.ts"], "sourcesContent": ["// Enums for reminder and appointment types\nexport enum MedicationFrequency {\n  ONCE = \"Once\",\n  TWICE = \"Twice\", \n  THREE_TIMES = \"Three times\"\n}\n\nexport enum AppointmentType {\n  CONSULTATION = \"Consultation\",\n  ONLINE = \"Online\",\n  IN_PERSON = \"In-Person\"\n}\n\nexport enum ActionType {\n  VIEW = \"view\",\n  EDIT = \"edit\", \n  DELETE = \"delete\",\n  COMPLETE = \"complete\"\n}"], "names": [], "mappings": "AAAA,2CAA2C;;;;;;AACpC,IAAA,AAAK,6CAAA;;;;WAAA;;AAML,IAAA,AAAK,yCAAA;;;;WAAA;;AAML,IAAA,AAAK,oCAAA;;;;;WAAA", "debugId": null}}, {"offset": {"line": 1526, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/mocks/remindersMockData.ts"], "sourcesContent": ["// Mock data for AI-Powered Reminders page\nimport { MedicationFrequency, AppointmentType } from './enums';\n\nexport const mockMedications = [\n  {\n    id: \"med-1\",\n    name: \"Lisnioril\",\n    dosage: \"200mg\",\n    time: new Date(\"2024-01-01T08:00:00\"),\n    frequency: MedicationFrequency.TWICE\n  },\n  {\n    id: \"med-2\", \n    name: \"Amoxicillin\",\n    dosage: \"500mg\",\n    time: new Date(\"2024-01-01T09:00:00\"),\n    frequency: MedicationFrequency.ONCE\n  },\n  {\n    id: \"med-3\",\n    name: \"Omeprazole\", \n    dosage: \"20mg\",\n    time: new Date(\"2024-01-01T08:30:00\"),\n    frequency: MedicationFrequency.ONCE\n  },\n  {\n    id: \"med-4\",\n    name: \"Atorvastatin\",\n    dosage: \"20mg\", \n    time: new Date(\"2024-01-01T19:00:00\"),\n    frequency: MedicationFrequency.ONCE\n  },\n  {\n    id: \"med-5\",\n    name: \"<PERSON><PERSON><PERSON><PERSON>\",\n    dosage: \"10mg\",\n    time: new Date(\"2024-01-01T18:00:00\"), \n    frequency: MedicationFrequency.ONCE\n  },\n  {\n    id: \"med-6\",\n    name: \"<PERSON><PERSON><PERSON>\",\n    dosage: \"500mg\",\n    time: new Date(\"2024-01-01T12:00:00\"),\n    frequency: MedicationFrequency.TWICE\n  },\n  {\n    id: \"med-7\",\n    name: \"Gabapentin\",\n    dosage: \"300mg\",\n    time: new Date(\"2024-01-01T21:30:00\"),\n    frequency: MedicationFrequency.THREE_TIMES\n  },\n  {\n    id: \"med-8\",\n    name: \"Ibuprofen\", \n    dosage: \"200mg\",\n    time: new Date(\"2024-01-01T10:00:00\"),\n    frequency: MedicationFrequency.TWICE\n  },\n  {\n    id: \"med-9\",\n    name: \"Amlodipine\",\n    dosage: \"5mg\",\n    time: new Date(\"2024-01-01T13:00:00\"),\n    frequency: MedicationFrequency.ONCE\n  },\n  {\n    id: \"med-10\",\n    name: \"Simvastatin\",\n    dosage: \"40mg\", \n    time: new Date(\"2024-01-01T21:00:00\"),\n    frequency: MedicationFrequency.ONCE\n  },\n  {\n    id: \"med-11\",\n    name: \"Lisinopril\",\n    dosage: \"10mg\",\n    time: new Date(\"2024-01-01T08:00:00\"),\n    frequency: MedicationFrequency.ONCE\n  }\n];\n\nexport const mockAppointments = [\n  {\n    id: \"appt-1\",\n    type: AppointmentType.CONSULTATION,\n    dateTime: new Date(\"2024-01-01T20:00:00\"),\n    provider: \"Dr Adewumi\"\n  },\n  {\n    id: \"appt-2\",\n    type: AppointmentType.CONSULTATION, \n    dateTime: new Date(\"2024-01-01T20:00:00\"),\n    provider: \"Dr Adewumi\"\n  },\n  {\n    id: \"appt-3\",\n    type: AppointmentType.ONLINE,\n    dateTime: new Date(\"2024-01-05T16:00:00\"),\n    provider: \"Dr Brown\"\n  },\n  {\n    id: \"appt-4\",\n    type: AppointmentType.ONLINE,\n    dateTime: new Date(\"2024-01-03T17:00:00\"),\n    provider: \"Dr Patel\"\n  },\n  {\n    id: \"appt-5\",\n    type: AppointmentType.IN_PERSON,\n    dateTime: new Date(\"2024-01-04T11:00:00\"),\n    provider: \"Dr Kim\"\n  },\n  {\n    id: \"appt-6\",\n    type: AppointmentType.IN_PERSON,\n    dateTime: new Date(\"2024-01-02T10:00:00\"),\n    provider: \"Dr Smith\"\n  },\n  {\n    id: \"appt-7\",\n    type: AppointmentType.IN_PERSON,\n    dateTime: new Date(\"2024-01-06T09:00:00\"),\n    provider: \"Dr Garcia\"\n  },\n  {\n    id: \"appt-8\",\n    type: AppointmentType.ONLINE,\n    dateTime: new Date(\"2024-01-08T15:00:00\"),\n    provider: \"Dr Johnson\"\n  },\n  {\n    id: \"appt-9\",\n    type: AppointmentType.ONLINE,\n    dateTime: new Date(\"2024-01-07T18:00:00\"),\n    provider: \"Dr Thompson\"\n  },\n  {\n    id: \"appt-10\",\n    type: AppointmentType.IN_PERSON,\n    dateTime: new Date(\"2024-01-09T13:00:00\"),\n    provider: \"Dr Lee\"\n  },\n  {\n    id: \"appt-11\",\n    type: AppointmentType.IN_PERSON,\n    dateTime: new Date(\"2024-01-15T14:00:00\"),\n    provider: \"Dr Wilson\"\n  }\n];\n\nexport const mockRootProps = {\n  activeTab: \"medications\" as const,\n  medications: mockMedications,\n  appointments: mockAppointments\n};"], "names": [], "mappings": "AAAA,0CAA0C;;;;;;AAC1C;;AAEO,MAAM,kBAAkB;IAC7B;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM,IAAI,KAAK;QACf,WAAW,wHAAA,CAAA,sBAAmB,CAAC,KAAK;IACtC;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM,IAAI,KAAK;QACf,WAAW,wHAAA,CAAA,sBAAmB,CAAC,IAAI;IACrC;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM,IAAI,KAAK;QACf,WAAW,wHAAA,CAAA,sBAAmB,CAAC,IAAI;IACrC;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM,IAAI,KAAK;QACf,WAAW,wHAAA,CAAA,sBAAmB,CAAC,IAAI;IACrC;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM,IAAI,KAAK;QACf,WAAW,wHAAA,CAAA,sBAAmB,CAAC,IAAI;IACrC;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM,IAAI,KAAK;QACf,WAAW,wHAAA,CAAA,sBAAmB,CAAC,KAAK;IACtC;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM,IAAI,KAAK;QACf,WAAW,wHAAA,CAAA,sBAAmB,CAAC,WAAW;IAC5C;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM,IAAI,KAAK;QACf,WAAW,wHAAA,CAAA,sBAAmB,CAAC,KAAK;IACtC;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM,IAAI,KAAK;QACf,WAAW,wHAAA,CAAA,sBAAmB,CAAC,IAAI;IACrC;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM,IAAI,KAAK;QACf,WAAW,wHAAA,CAAA,sBAAmB,CAAC,IAAI;IACrC;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM,IAAI,KAAK;QACf,WAAW,wHAAA,CAAA,sBAAmB,CAAC,IAAI;IACrC;CACD;AAEM,MAAM,mBAAmB;IAC9B;QACE,IAAI;QACJ,MAAM,wHAAA,CAAA,kBAAe,CAAC,YAAY;QAClC,UAAU,IAAI,KAAK;QACnB,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM,wHAAA,CAAA,kBAAe,CAAC,YAAY;QAClC,UAAU,IAAI,KAAK;QACnB,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM,wHAAA,CAAA,kBAAe,CAAC,MAAM;QAC5B,UAAU,IAAI,KAAK;QACnB,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM,wHAAA,CAAA,kBAAe,CAAC,MAAM;QAC5B,UAAU,IAAI,KAAK;QACnB,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM,wHAAA,CAAA,kBAAe,CAAC,SAAS;QAC/B,UAAU,IAAI,KAAK;QACnB,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM,wHAAA,CAAA,kBAAe,CAAC,SAAS;QAC/B,UAAU,IAAI,KAAK;QACnB,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM,wHAAA,CAAA,kBAAe,CAAC,SAAS;QAC/B,UAAU,IAAI,KAAK;QACnB,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM,wHAAA,CAAA,kBAAe,CAAC,MAAM;QAC5B,UAAU,IAAI,KAAK;QACnB,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM,wHAAA,CAAA,kBAAe,CAAC,MAAM;QAC5B,UAAU,IAAI,KAAK;QACnB,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM,wHAAA,CAAA,kBAAe,CAAC,SAAS;QAC/B,UAAU,IAAI,KAAK;QACnB,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM,wHAAA,CAAA,kBAAe,CAAC,SAAS;QAC/B,UAAU,IAAI,KAAK;QACnB,UAAU;IACZ;CACD;AAEM,MAAM,gBAAgB;IAC3B,WAAW;IACX,aAAa;IACb,cAAc;AAChB", "debugId": null}}, {"offset": {"line": 1695, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { RemindersPage } from '@/components/reminders/RemindersPage';\nimport { mockRootProps } from '@/mocks/remindersMockData';\n\nexport default function RemindersPreview() {\n  return (\n    <RemindersPage \n      medications={mockRootProps.medications}\n      appointments={mockRootProps.appointments}\n    />\n  );\n}"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAMe,SAAS;IACtB,qBACE,6LAAC,mJAAA,CAAA,gBAAa;QACZ,aAAa,oIAAA,CAAA,gBAAa,CAAC,WAAW;QACtC,cAAc,oIAAA,CAAA,gBAAa,CAAC,YAAY;;;;;;AAG9C;KAPwB", "debugId": null}}, {"offset": {"line": 1725, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js", "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/node_modules/lucide-react/src/icons/chevron-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm9 18 6-6-6-6', key: 'mthhwq' }]];\n\n/**\n * @component @name ChevronRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtOSAxOCA2LTYtNi02IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/chevron-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronRight = createLucideIcon('chevron-right', __iconNode);\n\nexport default ChevronRight;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,eAAiB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa9E,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1760, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/ellipsis.js", "sources": ["file:///C:/Users/<USER>/Documents/001WorkProject/petals_health.git/user_dashboard/user/node_modules/lucide-react/src/icons/ellipsis.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '1', key: '41hilf' }],\n  ['circle', { cx: '19', cy: '12', r: '1', key: '1wjl8i' }],\n  ['circle', { cx: '5', cy: '12', r: '1', key: '1pcz8c' }],\n];\n\n/**\n * @component @name Ellipsis\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxIiAvPgogIDxjaXJjbGUgY3g9IjE5IiBjeT0iMTIiIHI9IjEiIC8+CiAgPGNpcmNsZSBjeD0iNSIgY3k9IjEyIiByPSIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/ellipsis\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Ellipsis = createLucideIcon('ellipsis', __iconNode);\n\nexport default Ellipsis;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}]}