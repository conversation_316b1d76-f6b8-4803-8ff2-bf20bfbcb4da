import React from 'react';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '@/components/ui/table';
import { ActionButtons } from './ActionButtons';
import { formatDate, formatAppointmentType } from '@/lib/formatters';
import { AppointmentType } from '@/mocks/enums';

interface Appointment {
  id: string;
  type: AppointmentType;
  dateTime: Date;
  provider: string;
}

interface AppointmentsTableProps {
  appointments: Appointment[];
  onAction: (appointmentId: string, action: string) => void;
}

export function AppointmentsTable({ appointments, onAction }: AppointmentsTableProps) {
  return (
    <div className="health-table-container">
      <Table>
        <TableHeader>
          <TableRow className="health-table-header">
            <TableHead className="health-body-lg text-[#191919] font-normal px-10">Appointment Type</TableHead>
            <TableHead className="health-body-lg text-[#191919] font-normal">Date & Time</TableHead>
            <TableHead className="health-body-lg text-[#191919] font-normal">Provider</TableHead>
            <TableHead className="health-body-lg text-[#191919] font-normal">Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {appointments.map((appointment) => (
            <TableRow key={appointment.id} className="health-table-row">
              <TableCell className="health-button-text px-10">
                {formatAppointmentType(appointment.type)}
              </TableCell>
              <TableCell className="health-button-text">
                {formatDate(appointment.dateTime)}
              </TableCell>
              <TableCell className="health-button-text">{appointment.provider}</TableCell>
              <TableCell>
                <ActionButtons
                  itemId={appointment.id}
                  type="appointment"
                  onAction={onAction}
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}