'use client'

import React from 'react'
import { useAppContext, useAuth, useUI, useSettings, useNavigation, useForms, useSystem, useLoadingState, useModalState, useFormState } from './useAppContext'
import { StatusIndicator } from '@/components/ui/status-indicator'

// Development-only floating panel to inspect and interact with app context
export function ContextDemo() {
  // Using the main context hook to get full state access
  const state = useAppContext()
  
  // Using specialized hooks for specific functionality
  const { user, isAuthenticated, setUser, logout } = useAuth()
  const { theme, sidebarOpen, notifications, setTheme, toggleSidebar, addNotification } = useUI()
  const { settings, updateSettings } = useSettings()
  const { currentRoute, setCurrentRoute } = useNavigation()
  const { forms } = useForms()
  const { isOnline, isMobile } = useSystem()
  
  // Using advanced hooks for specific states
  const exampleLoading = useLoadingState('example-action')
  const { isOpen: exampleModalOpen, open: openExampleModal, close: closeExampleModal, toggle: toggleExampleModal } = useModalState('example-modal')
  const { formState: exampleFormState, setFormState: setExampleFormState, resetForm: resetExampleForm } = useFormState('example-form')

  const handleLogin = () => {
    const mockUser = {
      id: '1',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      role: 'patient' as const,
      isEmailVerified: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
    setUser(mockUser)
    addNotification({
      type: 'success',
      title: 'Welcome!',
      message: 'Successfully logged in with Zustand + Context',
      duration: 3000,
    })
  }

  const handleLogout = () => {
    logout()
    addNotification({
      type: 'info',
      title: 'Goodbye!',
      message: 'You have been logged out',
      duration: 3000,
    })
  }

  const handleThemeChange = () => {
    const next = theme === 'light' ? 'dark' : theme === 'dark' ? 'system' : 'light'
    setTheme(next)
    addNotification({
      type: 'info',
      title: 'Theme Changed',
      message: `Switched to ${next} theme`,
      duration: 2000,
    })
  }

  const handleFormExample = () => {
    setExampleFormState({
      dirty: true,
      isValid: false,
      errors: { email: 'Invalid email format' },
      touched: { email: true },
    })
  }

  const handleAsyncAction = async () => {
    state.setLoadingState('example-action', true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      addNotification({
        type: 'success',
        title: 'Success!',
        message: 'Async action completed successfully',
        duration: 5000,
      })
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Error!',
        message: 'Something went wrong',
        duration: 5000,
      })
    } finally {
      state.setLoadingState('example-action', false)
    }
  }

  // Floating debug panel pinned bottom-right to ensure visibility
  return (
    <div className="fixed bottom-4 right-4 z-50 w-[380px] max-h-[85vh] overflow-auto rounded-lg border bg-white shadow-xl text-sm">
      <div className="flex items-center justify-between px-3 py-2 border-b bg-gray-50">
        <h1 className="font-semibold">Zustand + Context Demo</h1>
        <StatusIndicator isOnline={isOnline} isMobile={isMobile} />
      </div>

      <div className="p-3 space-y-3">
        {/* Auth */}
        <section className="space-y-2">
          <div className="font-medium text-blue-700">Auth</div>
          <p>Status: {isAuthenticated ? 'Logged In' : 'Not Logged In'}</p>
          {user && (
            <div className="text-xs text-gray-700">
              <div>{user.firstName} {user.lastName} • {user.email}</div>
              <div>Role: {user.role}</div>
            </div>
          )}
          <div className="flex gap-2">
            <button onClick={handleLogin} className="px-2 py-1 rounded bg-blue-500 text-white hover:bg-blue-600 text-xs">Login</button>
            <button onClick={handleLogout} className="px-2 py-1 rounded bg-red-500 text-white hover:bg-red-600 text-xs">Logout</button>
          </div>
        </section>

        {/* UI */}
        <section className="space-y-2">
          <div className="font-medium text-green-700">UI</div>
          <div className="flex items-center gap-2">
            <span className="text-xs">Theme: {theme}</span>
            <button onClick={handleThemeChange} className="px-2 py-1 rounded bg-gray-800 text-white hover:bg-gray-900 text-xs">Toggle Theme</button>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-xs">Sidebar: {sidebarOpen ? 'Open' : 'Closed'}</span>
            <button onClick={toggleSidebar} className="px-2 py-1 rounded bg-green-600 text-white hover:bg-green-700 text-xs">Toggle Sidebar</button>
          </div>
          <div className="text-xs">Notifications: {notifications.length}</div>
          
          {/* Modal Example */}
          <div className="flex items-center gap-2">
            <span className="text-xs">Example Modal: {exampleModalOpen ? 'Open' : 'Closed'}</span>
            <button onClick={toggleExampleModal} className="px-2 py-1 rounded bg-indigo-600 text-white hover:bg-indigo-700 text-xs">Toggle Modal</button>
          </div>
        </section>

        {/* Settings */}
        <section className="space-y-2">
          <div className="font-medium text-purple-700">Settings</div>
          <div className="flex items-center gap-2">
            <span className="text-xs">Email Notifications: {settings.emailNotifications ? 'On' : 'Off'}</span>
            <button
              onClick={() => updateSettings({ emailNotifications: !settings.emailNotifications })}
              className="px-2 py-1 rounded bg-purple-600 text-white hover:bg-purple-700 text-xs"
            >
              Toggle Email
            </button>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-xs">Auto Save: {settings.autoSave ? 'On' : 'Off'}</span>
            <button
              onClick={() => updateSettings({ autoSave: !settings.autoSave })}
              className="px-2 py-1 rounded bg-purple-600 text-white hover:bg-purple-700 text-xs"
            >
              Toggle Auto Save
            </button>
          </div>
        </section>

        {/* Navigation */}
        <section className="space-y-2">
          <div className="font-medium text-orange-700">Navigation</div>
          <div className="flex items-center gap-2">
            <span className="truncate text-xs" title={currentRoute}>Current: {currentRoute}</span>
            <button
              onClick={() => setCurrentRoute('/dashboard')}
              className="px-2 py-1 rounded bg-orange-600 text-white hover:bg-orange-700 text-xs"
            >
              To /dashboard
            </button>
          </div>
        </section>

        {/* Forms */}
        <section className="space-y-2">
          <div className="font-medium text-teal-700">Forms</div>
          <div className="text-xs">Active Forms: {Object.keys(forms).length}</div>
          <div className="flex items-center gap-2">
            <span className="text-xs">Example Form: {exampleFormState ? 'Active' : 'Inactive'}</span>
            <button
              onClick={handleFormExample}
              className="px-2 py-1 rounded bg-teal-600 text-white hover:bg-teal-700 text-xs"
            >
              Set Form
            </button>
            {exampleFormState && (
              <button
                onClick={resetExampleForm}
                className="px-2 py-1 rounded bg-red-600 text-white hover:bg-red-700 text-xs"
              >
                Reset
              </button>
            )}
          </div>
          {exampleFormState && (
            <div className="text-xs text-gray-600">
              Valid: {exampleFormState.isValid ? 'Yes' : 'No'}, 
              Dirty: {exampleFormState.dirty ? 'Yes' : 'No'}
            </div>
          )}
        </section>

        {/* Loading States */}
        <section className="space-y-2">
          <div className="font-medium text-indigo-700">Loading States</div>
          <div className="flex items-center gap-2">
            <span className="text-xs">Example Action: {exampleLoading ? 'Loading...' : 'Idle'}</span>
            <button
              onClick={handleAsyncAction}
              disabled={exampleLoading}
              className="px-2 py-1 rounded bg-indigo-600 text-white hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed text-xs"
            >
              {exampleLoading ? 'Loading...' : 'Start Action'}
            </button>
          </div>
        </section>

        {/* System Info */}
        <section className="space-y-2">
          <div className="font-medium text-gray-700">System</div>
          <div className="text-xs">
            Online: {isOnline ? 'Yes' : 'No'} | Mobile: {isMobile ? 'Yes' : 'No'}
          </div>
        </section>

        {/* State Debug */}
        <section className="space-y-2">
          <div className="font-medium text-gray-700">State (Zustand + Context)</div>
          <pre className="bg-gray-100 p-2 rounded text-[10px] overflow-auto max-h-32">
            {JSON.stringify({
              auth: state.auth,
              ui: {
                ...state.ui,
                notifications: `${state.ui.notifications.length} items`
              },
              settings: state.settings,
              navigation: state.navigation,
              system: { isOnline: state.isOnline, isMobile: state.isMobile },
              forms: Object.keys(state.forms)
            }, null, 2)}
          </pre>
        </section>
      </div>
    </div>
  )
}

// Example of how to wrap your app with the provider
export function AppWrapper({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen">
      {children}
    </div>
  )
}

// Example usage in a component showing the power of the hybrid approach
export function ExampleComponent() {
  const { addNotification } = useUI()
  const { user } = useAuth()
  const exampleLoading = useLoadingState('example-action')
  const { isOpen: modalOpen, open: openModal, close: closeModal } = useModalState('example-modal')

  const handleAsyncAction = async () => {
    const store = useAppContext()
    store.setLoadingState('example-action', true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      addNotification({
        type: 'success',
        title: 'Success!',
        message: 'Action completed successfully with Zustand + Context',
        duration: 5000,
      })
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Error!',
        message: 'Something went wrong',
        duration: 5000,
      })
    } finally {
      store.setLoadingState('example-action', false)
    }
  }

  return (
    <div className="p-4 space-y-4">
      <h2 className="text-xl font-bold">Example Component</h2>
      <p>Welcome, {user?.firstName || 'Guest'}!</p>
      
      <div className="space-x-2">
        <button 
          onClick={handleAsyncAction}
          disabled={exampleLoading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {exampleLoading ? 'Loading...' : 'Perform Async Action'}
        </button>
        
        <button 
          onClick={openModal}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
        >
          Open Modal
        </button>
      </div>

      {modalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg">
            <h3 className="text-lg font-bold mb-4">Example Modal</h3>
            <p className="mb-4">This modal state is managed by Zustand + Context!</p>
            <button 
              onClick={closeModal}
              className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
            >
              Close Modal
            </button>
          </div>
        </div>
      )}
    </div>
  )
}