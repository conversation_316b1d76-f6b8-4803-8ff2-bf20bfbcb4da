"use client";

interface PersonalizedHeaderProps {
  userName: string;
  currentDate: Date;
}

export function PersonalizedHeader({ userName, currentDate }: PersonalizedHeaderProps) {
  const formatDateTime = (date: Date): string => {
    return date.toLocaleDateString('en-US', { 
      weekday: 'long', 
      month: 'long', 
      day: 'numeric' 
    }) + ' - ' + date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  };

  return (
    <header className="flex items-center justify-between mb-6">
      <div className="space-y-1">
        <h1 className="text-5xl font-normal" style={{ color: 'var(--health-secondary)' }}>
          Hello, {userName}
        </h1>
        <p className="text-sm" style={{ color: 'var(--health-muted)' }}>
          Every step towards wellness counts. You've got this
        </p>
      </div>
      <div className="health-caption">
        {formatDateTime(currentDate)}
      </div>
    </header>
  );
}