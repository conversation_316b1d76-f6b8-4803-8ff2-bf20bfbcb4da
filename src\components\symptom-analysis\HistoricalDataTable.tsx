"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { formatDate } from "@/mocks/symptomAnalysisMockData";

interface HistoricalEntry {
  dateLogged: Date;
  severity: string;
  aiSummary: string;
}

interface HistoricalDataTableProps {
  data: HistoricalEntry[];
  analysisText: string;
}

export function HistoricalDataTable({ data, analysisText }: HistoricalDataTableProps) {
  return (
    <div className="space-y-4">
      <h3 className="health-heading-sm">AI Analysis Summary</h3>
      
      <div className="space-y-4">
        <p className="health-body-lg">{analysisText}</p>
        <p className="health-body-lg">{analysisText}</p>
        
        <Card className="health-card-shadow rounded-lg">
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow className="border-b border-[#eff1f3]">
                  <TableHead className="health-body-lg text-[#191919] font-normal px-6 py-4">
                    Date Logged
                  </TableHead>
                  <TableHead className="health-body-lg text-[#191919] font-normal px-6 py-4">
                    Severity
                  </TableHead>
                  <TableHead className="health-body-lg text-[#191919] font-normal px-6 py-4">
                    Brief AI Summary
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.map((entry, index) => (
                  <TableRow 
                    key={index} 
                    className={`border-b border-[#eff1f3] ${index === data.length - 1 ? 'rounded-b-lg' : ''}`}
                  >
                    <TableCell className="health-button-text px-6 py-4">
                      {formatDate(entry.dateLogged)}
                    </TableCell>
                    <TableCell className="health-button-text px-6 py-4">
                      {entry.severity}
                    </TableCell>
                    <TableCell className="health-button-text px-6 py-4">
                      {entry.aiSummary}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}