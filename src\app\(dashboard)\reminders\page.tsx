// "use client";

// import { Breadcrumb, Bread<PERSON><PERSON>bI<PERSON>, B<PERSON><PERSON><PERSON>bLink, BreadcrumbList } from "@/components/ui/breadcrumb";
// import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
// import { Button } from "@/components/ui/button";
// import ChevronRightIcon from "@/components/icons/ChevronRightIcon";
// import ClockIcon from "@/components/icons/ClockIcon";

// export default function RemindersPage() {
//   const upcomingReminders = [
//     {
//       id: 1,
//       medication: "Ibuprofen",
//       time: "9:00 AM",
//       date: "Today",
//       dosage: "200mg",
//       type: "Pain Relief"
//     },
//     {
//       id: 2,
//       medication: "Vitamin D",
//       time: "12:00 PM",
//       date: "Today",
//       dosage: "1000 IU",
//       type: "Supplement"
//     },
//     {
//       id: 3,
//       medication: "Magnesium Glycinate",
//       time: "8:00 PM",
//       date: "Today",
//       dosage: "400mg",
//       type: "Supplement"
//     }
//   ];

//   return (
//     <div className="w-full min-h-screen p-6 sm:p-10 space-y-6 bg-health-background">
//       {/* Breadcrumb */}
//       <Breadcrumb>
//         <BreadcrumbList>
//           <BreadcrumbItem>
//             <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
//           </BreadcrumbItem>
//           <ChevronRightIcon width={16} height={16} color="#6b7280" />
//           <BreadcrumbItem>
//             <span className="text-health-primary font-medium">AI Reminders</span>
//           </BreadcrumbItem>
//         </BreadcrumbList>
//       </Breadcrumb>

//       {/* Page Header */}
//       <div className="space-y-2">
//         <h1 className="health-heading-xl">AI Reminders</h1>
//         <p className="health-body-lg text-health-secondary">
//           Manage your medication and supplement reminders powered by AI
//         </p>
//       </div>

//       {/* Upcoming Reminders */}
//       <Card className="health-card-shadow health-rounded">
//         <CardHeader>
//           <CardTitle className="flex items-center gap-2">
//             <ClockIcon width={20} height={20} color="#455f84" />
//             Today's Reminders
//           </CardTitle>
//         </CardHeader>
//         <CardContent className="space-y-4">
//           {upcomingReminders.map((reminder) => (
//             <div key={reminder.id} className="flex items-center justify-between p-4 border border-health-border rounded-lg">
//               <div className="space-y-1">
//                 <h3 className="health-body-lg font-medium">{reminder.medication}</h3>
//                 <p className="health-body-sm text-health-secondary">
//                   {reminder.dosage} • {reminder.type}
//                 </p>
//                 <p className="health-body-sm text-health-accent">
//                   {reminder.time} • {reminder.date}
//                 </p>
//               </div>
//               <div className="flex gap-2">
//                 <Button variant="outline" size="sm">
//                   Skip
//                 </Button>
//                 <Button size="sm">
//                   Mark Taken
//                 </Button>
//               </div>
//             </div>
//           ))}
//         </CardContent>
//       </Card>

//       {/* Quick Actions */}
//       <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//         <Card className="health-card-shadow health-rounded">
//           <CardHeader>
//             <CardTitle>Add New Reminder</CardTitle>
//           </CardHeader>
//           <CardContent>
//             <Button className="w-full">
//               Create Reminder
//             </Button>
//           </CardContent>
//         </Card>

//         <Card className="health-card-shadow health-rounded">
//           <CardHeader>
//             <CardTitle>Reminder Settings</CardTitle>
//           </CardHeader>
//           <CardContent>
//             <Button variant="outline" className="w-full">
//               Manage Settings
//             </Button>
//           </CardContent>
//         </Card>
//       </div>
//     </div>
//   );
// }

"use client";

import React from 'react';
import { RemindersPage } from '@/components/reminders/RemindersPage';
import { mockRootProps } from '@/mocks/remindersMockData';

export default function RemindersPreview() {
  return (
    <RemindersPage 
      medications={mockRootProps.medications}
      appointments={mockRootProps.appointments}
    />
  );
}