"use client";

import { Card, CardContent } from "@/components/ui/card";
import { formatBPM, formatHours } from "@/mocks/symptomAnalysisMockData";

interface AIInsightSectionProps {
  heartRate: number;
  airQuality: string;
  sleepHours: number;
  analysisText: string;
}

export function AIInsightSection({ heartRate, airQuality, sleepHours, analysisText }: AIInsightSectionProps) {
  return (
    <div className="space-y-4">
      <h2 className="health-heading-lg">AI Insight</h2>
      
      <div className="space-y-4">
        <h3 className="health-heading-sm">AI Generated Contextual Data</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="health-card-shadow health-rounded p-4">
            <CardContent className="p-0 space-y-2">
              <p className="health-body-lg">Your recent heart rate from wearable:</p>
              <p className="health-heading-sm">{formatBPM(heartRate)}</p>
            </CardContent>
          </Card>
          
          <Card className="health-card-shadow health-rounded p-4">
            <CardContent className="p-0 space-y-2">
              <p className="health-body-lg">Local air quality</p>
              <p className="health-heading-sm">{airQuality}</p>
            </CardContent>
          </Card>
          
          <Card className="health-card-shadow health-rounded p-4">
            <CardContent className="p-0 space-y-2">
              <p className="health-body-lg">Last logged sleep</p>
              <p className="health-heading-sm">{formatHours(sleepHours)}</p>
            </CardContent>
          </Card>
        </div>
      </div>
      
      <div className="space-y-4">
        <h3 className="health-heading-sm">AI Analysis Summary</h3>
        <div className="space-y-4">
          <p className="health-body-lg">{analysisText}</p>
          <p className="health-body-lg">{analysisText}</p>
        </div>
      </div>
    </div>
  );
}